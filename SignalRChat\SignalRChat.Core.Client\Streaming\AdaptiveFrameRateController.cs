using SignalRChat.Core.Client.Configuration;
using SignalRChat.Core.Client.Performance;

namespace SignalRChat.Core.Client.Streaming
{
    /// <summary>
    /// Adaptive frame rate controller that adjusts FPS based on system performance
    /// </summary>
    public class AdaptiveFrameRateController : IDisposable
    {
        private readonly StreamingConfiguration _config;
        private readonly PerformanceMonitor _performanceMonitor;
        private readonly Timer _adjustmentTimer;
        private readonly object _lockObject = new();

        private int _currentFrameRate;
        private DateTime _lastAdjustment;
        private int _consecutiveDrops;
        private int _consecutiveGoodFrames;
        private bool _disposed;

        public event Action<int>? FrameRateChanged;

        public int CurrentFrameRate 
        { 
            get 
            { 
                lock (_lockObject) 
                { 
                    return _currentFrameRate; 
                } 
            } 
            private set 
            { 
                lock (_lockObject) 
                { 
                    if (_currentFrameRate != value)
                    {
                        _currentFrameRate = value;
                        FrameRateChanged?.Invoke(value);
                    }
                } 
            } 
        }

        public bool IsAdaptive { get; set; } = true;

        public AdaptiveFrameRateController(StreamingConfiguration config, PerformanceMonitor performanceMonitor)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _performanceMonitor = performanceMonitor ?? throw new ArgumentNullException(nameof(performanceMonitor));
            
            _currentFrameRate = config.TargetFrameRate;
            _lastAdjustment = DateTime.Now;
            
            _adjustmentTimer = new Timer(EvaluateAndAdjust, null, 
                TimeSpan.FromSeconds(2), TimeSpan.FromSeconds(1));
        }

        /// <summary>
        /// Evaluates current performance and adjusts frame rate if needed
        /// </summary>
        private void EvaluateAndAdjust(object? state)
        {
            if (_disposed || !IsAdaptive || !_config.AdaptiveFrameRate)
                return;

            try
            {
                var metrics = _performanceMonitor.CurrentMetrics;
                var shouldDecrease = ShouldDecreaseFrameRate(metrics);
                var shouldIncrease = ShouldIncreaseFrameRate(metrics);

                if (shouldDecrease)
                {
                    DecreaseFrameRate();
                    _consecutiveDrops++;
                    _consecutiveGoodFrames = 0;
                }
                else if (shouldIncrease)
                {
                    IncreaseFrameRate();
                    _consecutiveGoodFrames++;
                    _consecutiveDrops = 0;
                }
                else
                {
                    _consecutiveDrops = Math.Max(0, _consecutiveDrops - 1);
                    _consecutiveGoodFrames = Math.Max(0, _consecutiveGoodFrames - 1);
                }
            }
            catch (Exception ex)
            {
                // Log error but continue operation
                System.Diagnostics.Debug.WriteLine($"Error in frame rate adjustment: {ex.Message}");
            }
        }

        /// <summary>
        /// Determines if frame rate should be decreased
        /// </summary>
        private bool ShouldDecreaseFrameRate(PerformanceMetrics metrics)
        {
            // Check if we're at minimum already
            if (CurrentFrameRate <= _config.MinFrameRate)
                return false;

            // Check CPU usage
            if (metrics.CpuUsagePercent > _config.MaxCpuUsagePercent)
                return true;

            // Check memory usage
            if (metrics.MemoryUsageMB > _config.MaxMemoryUsageMB)
                return true;

            // Check if frames are being dropped frequently
            if (metrics.DroppedFrames > 0 && _consecutiveDrops >= 3)
                return true;

            // Check frame processing time
            var targetFrameTime = TimeSpan.FromMilliseconds(1000.0 / CurrentFrameRate);
            if (metrics.FrameProcessingTime > targetFrameTime.Multiply(1.5))
                return true;

            // Check network bandwidth
            var estimatedBandwidth = EstimateRequiredBandwidth();
            if (estimatedBandwidth > _config.MaxBandwidthKbps * 0.9)
                return true;

            return false;
        }

        /// <summary>
        /// Determines if frame rate should be increased
        /// </summary>
        private bool ShouldIncreaseFrameRate(PerformanceMetrics metrics)
        {
            // Check if we're at maximum already
            if (CurrentFrameRate >= _config.MaxFrameRate)
                return false;

            // Only increase if we've had good performance for a while
            if (_consecutiveGoodFrames < 10)
                return false;

            // Check if system has headroom
            if (metrics.CpuUsagePercent < _config.MaxCpuUsagePercent * 0.7 &&
                metrics.MemoryUsageMB < _config.MaxMemoryUsageMB * 0.8 &&
                metrics.DroppedFrames == 0)
            {
                // Check frame processing time
                var targetFrameTime = TimeSpan.FromMilliseconds(1000.0 / (CurrentFrameRate + 1));
                if (metrics.FrameProcessingTime < targetFrameTime.Multiply(0.8))
                {
                    // Check network bandwidth
                    var estimatedBandwidth = EstimateRequiredBandwidth(CurrentFrameRate + 1);
                    if (estimatedBandwidth < _config.MaxBandwidthKbps * 0.7)
                        return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Decreases the current frame rate
        /// </summary>
        private void DecreaseFrameRate()
        {
            var newFrameRate = Math.Max(_config.MinFrameRate, CurrentFrameRate - 1);
            
            // More aggressive decrease if system is really struggling
            if (_consecutiveDrops > 5)
                newFrameRate = Math.Max(_config.MinFrameRate, CurrentFrameRate - 2);

            CurrentFrameRate = newFrameRate;
            _lastAdjustment = DateTime.Now;
        }

        /// <summary>
        /// Increases the current frame rate
        /// </summary>
        private void IncreaseFrameRate()
        {
            var newFrameRate = Math.Min(_config.MaxFrameRate, CurrentFrameRate + 1);
            CurrentFrameRate = newFrameRate;
            _lastAdjustment = DateTime.Now;
        }

        /// <summary>
        /// Estimates required bandwidth for current or specified frame rate
        /// </summary>
        private long EstimateRequiredBandwidth(int? frameRate = null)
        {
            var fps = frameRate ?? CurrentFrameRate;
            var metrics = _performanceMonitor.CurrentMetrics;
            
            if (metrics.AverageFrameSize > 0)
            {
                var bytesPerSecond = metrics.AverageFrameSize * fps;
                return (long)(bytesPerSecond * 8 / 1024); // Convert to Kbps
            }

            // Fallback estimation based on resolution and quality
            var pixelCount = _config.Width * _config.Height;
            var estimatedBytesPerFrame = pixelCount * GetBytesPerPixelEstimate();
            var bytesPerSecond = estimatedBytesPerFrame * fps;
            return (long)(bytesPerSecond * 8 / 1024);
        }

        /// <summary>
        /// Gets estimated bytes per pixel based on quality settings
        /// </summary>
        private double GetBytesPerPixelEstimate()
        {
            return _config.Quality switch
            {
                VideoQuality.Low => 0.1,
                VideoQuality.Medium => 0.2,
                VideoQuality.High => 0.3,
                VideoQuality.Ultra => 0.5,
                _ => 0.2
            };
        }

        /// <summary>
        /// Forces a specific frame rate (disables adaptive behavior temporarily)
        /// </summary>
        public void SetFrameRate(int frameRate)
        {
            frameRate = Math.Clamp(frameRate, _config.MinFrameRate, _config.MaxFrameRate);
            CurrentFrameRate = frameRate;
            _consecutiveDrops = 0;
            _consecutiveGoodFrames = 0;
        }

        /// <summary>
        /// Resets to target frame rate
        /// </summary>
        public void Reset()
        {
            CurrentFrameRate = _config.TargetFrameRate;
            _consecutiveDrops = 0;
            _consecutiveGoodFrames = 0;
            _lastAdjustment = DateTime.Now;
        }

        /// <summary>
        /// Gets the current frame interval in milliseconds
        /// </summary>
        public int GetFrameIntervalMs()
        {
            return (int)(1000.0 / CurrentFrameRate);
        }

        /// <summary>
        /// Gets frame rate statistics
        /// </summary>
        public (int Current, int Target, int Min, int Max, DateTime LastAdjustment) GetStatistics()
        {
            lock (_lockObject)
            {
                return (CurrentFrameRate, _config.TargetFrameRate, _config.MinFrameRate, 
                       _config.MaxFrameRate, _lastAdjustment);
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            _disposed = true;
            _adjustmentTimer?.Dispose();
        }
    }
}
