﻿namespace SignalRChat.Core.Server.Data.Repositories
{
    public class EFCoreRepository : IRepository
    {
        private readonly ApplicationDbContext _context;

        public EFCoreRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<TDto>> GetClientsByType<T, TDto>() where T : Client where TDto : ClientDto
        {
            List<TDto> list = await _context.Clients.OfType<T>()
                .Select(client => (TDto)Activator.CreateInstance(typeof(TDto), client)!)
                .ToListAsync();
            return list;
        }

        public async Task<TDto?> GetClientByType<T, TDto>(Ulid clientId) where T : Client where TDto : ClientDto
        {
            T? client = await _context.Clients.OfType<T>()
                .FirstOrDefaultAsync(x => x.ClientId.Equals(clientId));
            if (client != null)
                return (TDto?)Activator.CreateInstance(typeof(TDto), client);
            return null;
        }

        public async Task<TDto?> GetClientWithMessagesByType<T, TDto>(Ulid clientId) where T : Client where TDto : ClientDto
        {
            T? client = await _context.Clients.OfType<T>()
                .Include(x => x.SentMessages).ThenInclude(x => x.Receiver) // Include Receiver for sent messages
                .Include(x => x.ReceivedMessages).ThenInclude(x => x.Sender) // Include Sender for received messages
                .FirstOrDefaultAsync(x => x.ClientId.Equals(clientId));
            if (client != null)
                return (TDto?)Activator.CreateInstance(typeof(TDto), client);
            return null;
        }

        public async Task<List<UserDto>> GetUsersWithMessages()
        {
            return await _context.Clients.OfType<User>()
                .Include(x => x.SentMessages).ThenInclude(x => x.Receiver) // Include Receiver for sent messages
                .Include(x => x.ReceivedMessages).ThenInclude(x => x.Sender) // Include Sender for received messages
                .Select(user => new UserDto(user))
                .ToListAsync();
        }

        public async Task<List<GroupDto>> GetGroups()
        {
            return await _context.Clients.OfType<Group>()
                .Select(x => new GroupDto(x))
                .ToListAsync();
        }

        public async Task<List<GroupDto>> GetGroupsWithMessages()
        {
            return await _context.Clients.OfType<Group>()
                .Include(x => x.SentMessages).ThenInclude(x => x.Receiver) // Include Receiver for sent messages
                .Include(x => x.ReceivedMessages).ThenInclude(x => x.Sender) // Include Sender for received messages
                .Select(group => new GroupDto(group))
                .ToListAsync();
        }

        public async Task<bool> AddClient(ClientDto clientDto)
        {
            var currentClient = await _context.Clients.FirstOrDefaultAsync(x => x.ClientId == clientDto.ClientId);
            if (currentClient == null)
            {
                Client client = null!;
                switch (clientDto)
                {
                    case UserDto userDto:
                        client = new User(userDto);
                        break;
                    case GroupDto groupDto:
                        client = new Group(groupDto);
                        break;
                    default:
                        client = new Client(clientDto);
                        break;
                }
                await _context.Clients.AddAsync(client);
                await _context.SaveChangesAsync();
                return true;
            }

            return await UpdateClient(clientDto);
        }

        public async Task<bool> UpdateClient(ClientDto clientDto)
        {
            var currentClient = await _context.Clients.FirstOrDefaultAsync(x => x.ClientId == clientDto.ClientId);
            if (currentClient != null)
            {
                currentClient.UpdateConnectionId(clientDto.ConnectionId);
                await _context.SaveChangesAsync();
                return true;
            }

            return false;
        }

        public async Task<bool> DeleteClient(Ulid clientId)
        {
            var currentClient = await _context.Clients.FirstOrDefaultAsync(x => x.ClientId == clientId);
            if (currentClient != null)
            {
                _context.Clients.Remove(currentClient);
                await _context.SaveChangesAsync();
                return true;
            }

            return false;
        }

        public async Task<bool> AddMessage(IChatMessageResponse response)
        {
            if (!response.Savable)
                return true;
            ChatMessage chatMessage = null!;
            switch (response)
            {
                case TextMessageResponse textMessageResponse:
                    chatMessage = new TextChatMessage(textMessageResponse);
                    break;
                case ImageMessageResponse imageMessageResponse:
                    chatMessage = new ImageChatMessage(imageMessageResponse);
                    break;
                case RecordMessageResponse recordMessageResponse:
                    chatMessage = new RecordChatMessage(recordMessageResponse);
                    break;
                case StreamMessageResponse streamMessageResponse:
                    chatMessage = new StreamChatMessage(streamMessageResponse);
                    break;
                default:
                    chatMessage = new ChatMessage(response, MessageType.Unknown);
                    break;
            }
            await _context.ChatMessages.AddAsync(chatMessage);
            await _context.SaveChangesAsync();
            return true;
        }
    }
}
