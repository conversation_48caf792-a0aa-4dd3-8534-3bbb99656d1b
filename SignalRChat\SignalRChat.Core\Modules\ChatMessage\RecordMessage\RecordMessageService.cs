﻿namespace SignalRChat.Core.Modules.ChatMessage.RecordMessage
{
    public record RecordMessageRequest(Ulid MessageId, Ulid SenderId, Ulid ReceiverId, byte[] Record) : BaseChatMessageRequest(MessageId, SenderId, ReceiverId)
    {
        public override bool IsValid()
        {
            return
                base.IsValid()
                && Record != null
                && Record.Length > 0;
        }
    }

    public record RecordMessageResponse(Ulid MessageId, Ulid SenderId, Ulid ReceiverId, byte[] Record) : BaseChatMessageResponse(MessageId, SenderId, ReceiverId, true);
}
