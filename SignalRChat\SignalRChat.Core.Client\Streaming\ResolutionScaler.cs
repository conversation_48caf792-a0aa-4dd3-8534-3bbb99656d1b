using SignalRChat.Core.Client.Configuration;
using SignalRChat.Core.Client.Performance;
using System.Drawing.Drawing2D;

namespace SignalRChat.Core.Client.Streaming
{
    /// <summary>
    /// Resolution preset definitions
    /// </summary>
    public static class ResolutionPresets
    {
        public static readonly (int Width, int Height) SD = (640, 480);
        public static readonly (int Width, int Height) HD = (1280, 720);
        public static readonly (int Width, int Height) FullHD = (1920, 1080);
        public static readonly (int Width, int Height) QHD = (2560, 1440);
        public static readonly (int Width, int Height) UHD = (3840, 2160);

        public static (int Width, int Height) GetPreset(VideoQuality quality)
        {
            return quality switch
            {
                VideoQuality.Low => SD,
                VideoQuality.Medium => HD,
                VideoQuality.High => FullHD,
                VideoQuality.Ultra => QHD,
                _ => HD
            };
        }

        public static VideoQuality GetQualityForResolution(int width, int height)
        {
            var pixelCount = width * height;
            
            if (pixelCount <= SD.Width * SD.Height) return VideoQuality.Low;
            if (pixelCount <= HD.Width * HD.Height) return VideoQuality.Medium;
            if (pixelCount <= FullHD.Width * FullHD.Height) return VideoQuality.High;
            return VideoQuality.Ultra;
        }
    }

    /// <summary>
    /// Dynamic resolution scaler with performance-based adaptation
    /// </summary>
    public class ResolutionScaler : IDisposable
    {
        private readonly StreamingConfiguration _config;
        private readonly PerformanceMonitor _performanceMonitor;
        private readonly Timer _adaptationTimer;
        private readonly object _lockObject = new();

        private (int Width, int Height) _currentResolution;
        private (int Width, int Height) _targetResolution;
        private double _currentScaleFactor;
        private DateTime _lastAdaptation;
        private int _consecutivePerformanceIssues;
        private bool _disposed;

        public event Action<int, int>? ResolutionChanged;

        public (int Width, int Height) CurrentResolution 
        { 
            get 
            { 
                lock (_lockObject) 
                { 
                    return _currentResolution; 
                } 
            } 
            private set 
            { 
                lock (_lockObject) 
                { 
                    if (_currentResolution != value)
                    {
                        _currentResolution = value;
                        ResolutionChanged?.Invoke(value.Width, value.Height);
                    }
                } 
            } 
        }

        public double CurrentScaleFactor => _currentScaleFactor;
        public bool IsAdaptive { get; set; } = true;

        public ResolutionScaler(StreamingConfiguration config, PerformanceMonitor performanceMonitor)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _performanceMonitor = performanceMonitor ?? throw new ArgumentNullException(nameof(performanceMonitor));

            _targetResolution = (config.Width, config.Height);
            _currentResolution = _targetResolution;
            _currentScaleFactor = config.ScaleFactor;
            _lastAdaptation = DateTime.Now;

            _adaptationTimer = new Timer(EvaluateAndAdapt, null, 
                TimeSpan.FromSeconds(3), TimeSpan.FromSeconds(2));
        }

        /// <summary>
        /// Evaluates performance and adapts resolution if needed
        /// </summary>
        private void EvaluateAndAdapt(object? state)
        {
            if (_disposed || !IsAdaptive || !_config.AdaptiveResolution)
                return;

            try
            {
                var metrics = _performanceMonitor.CurrentMetrics;
                var shouldDecrease = ShouldDecreaseResolution(metrics);
                var shouldIncrease = ShouldIncreaseResolution(metrics);

                if (shouldDecrease)
                {
                    DecreaseResolution();
                    _consecutivePerformanceIssues++;
                }
                else if (shouldIncrease)
                {
                    IncreaseResolution();
                    _consecutivePerformanceIssues = 0;
                }
                else
                {
                    _consecutivePerformanceIssues = Math.Max(0, _consecutivePerformanceIssues - 1);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in resolution adaptation: {ex.Message}");
            }
        }

        /// <summary>
        /// Determines if resolution should be decreased
        /// </summary>
        private bool ShouldDecreaseResolution(PerformanceMetrics metrics)
        {
            // Check if we're already at minimum resolution
            if (_currentScaleFactor <= 0.3) return false;

            // Check CPU usage
            if (metrics.CpuUsagePercent > _config.MaxCpuUsagePercent * 0.9)
                return true;

            // Check memory usage
            if (metrics.MemoryUsageMB > _config.MaxMemoryUsageMB * 0.9)
                return true;

            // Check frame processing time
            var targetFrameTime = TimeSpan.FromMilliseconds(1000.0 / _config.TargetFrameRate);
            if (metrics.FrameProcessingTime > targetFrameTime.Multiply(2))
                return true;

            // Check for dropped frames
            if (metrics.DroppedFrames > 0 && _consecutivePerformanceIssues >= 2)
                return true;

            return false;
        }

        /// <summary>
        /// Determines if resolution should be increased
        /// </summary>
        private bool ShouldIncreaseResolution(PerformanceMetrics metrics)
        {
            // Check if we're already at target resolution
            if (_currentScaleFactor >= _config.ScaleFactor) return false;

            // Only increase if performance has been good for a while
            if (_consecutivePerformanceIssues > 0) return false;

            // Check if system has headroom
            if (metrics.CpuUsagePercent < _config.MaxCpuUsagePercent * 0.6 &&
                metrics.MemoryUsageMB < _config.MaxMemoryUsageMB * 0.7 &&
                metrics.DroppedFrames == 0)
            {
                var targetFrameTime = TimeSpan.FromMilliseconds(1000.0 / _config.TargetFrameRate);
                if (metrics.FrameProcessingTime < targetFrameTime.Multiply(0.5))
                    return true;
            }

            return false;
        }

        /// <summary>
        /// Decreases the current resolution
        /// </summary>
        private void DecreaseResolution()
        {
            var newScaleFactor = Math.Max(0.3, _currentScaleFactor - 0.1);
            
            // More aggressive decrease if system is really struggling
            if (_consecutivePerformanceIssues > 3)
                newScaleFactor = Math.Max(0.3, _currentScaleFactor - 0.2);

            ApplyScaleFactor(newScaleFactor);
        }

        /// <summary>
        /// Increases the current resolution
        /// </summary>
        private void IncreaseResolution()
        {
            var newScaleFactor = Math.Min(_config.ScaleFactor, _currentScaleFactor + 0.1);
            ApplyScaleFactor(newScaleFactor);
        }

        /// <summary>
        /// Applies a new scale factor
        /// </summary>
        private void ApplyScaleFactor(double scaleFactor)
        {
            _currentScaleFactor = scaleFactor;
            
            var newWidth = (int)(_targetResolution.Width * scaleFactor);
            var newHeight = (int)(_targetResolution.Height * scaleFactor);
            
            // Ensure dimensions are even numbers for better compression
            newWidth = (newWidth / 2) * 2;
            newHeight = (newHeight / 2) * 2;
            
            CurrentResolution = (newWidth, newHeight);
            _lastAdaptation = DateTime.Now;
        }

        /// <summary>
        /// Scales a bitmap to the current resolution
        /// </summary>
        public Bitmap ScaleBitmap(Bitmap source)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(ResolutionScaler));

            var targetRes = CurrentResolution;
            
            // If no scaling needed, return copy
            if (source.Width == targetRes.Width && source.Height == targetRes.Height)
                return new Bitmap(source);

            // Calculate aspect-ratio preserving dimensions
            var sourceAspect = (double)source.Width / source.Height;
            var targetAspect = (double)targetRes.Width / targetRes.Height;

            int finalWidth, finalHeight;
            if (sourceAspect > targetAspect)
            {
                finalWidth = targetRes.Width;
                finalHeight = (int)(targetRes.Width / sourceAspect);
            }
            else
            {
                finalWidth = (int)(targetRes.Height * sourceAspect);
                finalHeight = targetRes.Height;
            }

            // Create scaled bitmap
            var scaledBitmap = new Bitmap(finalWidth, finalHeight, source.PixelFormat);
            
            using (var graphics = Graphics.FromImage(scaledBitmap))
            {
                // Set high-quality scaling
                graphics.InterpolationMode = GetInterpolationMode();
                graphics.SmoothingMode = SmoothingMode.HighQuality;
                graphics.PixelOffsetMode = PixelOffsetMode.HighQuality;
                graphics.CompositingQuality = CompositingQuality.HighQuality;

                graphics.DrawImage(source, 0, 0, finalWidth, finalHeight);
            }

            return scaledBitmap;
        }

        /// <summary>
        /// Gets interpolation mode based on performance settings
        /// </summary>
        private InterpolationMode GetInterpolationMode()
        {
            return _config.PerformanceMode switch
            {
                PerformanceMode.PowerSaver => InterpolationMode.Low,
                PerformanceMode.Balanced => InterpolationMode.HighQualityBilinear,
                PerformanceMode.Performance => InterpolationMode.HighQualityBicubic,
                PerformanceMode.MaxPerformance => InterpolationMode.HighQualityBicubic,
                _ => InterpolationMode.HighQualityBilinear
            };
        }

        /// <summary>
        /// Sets a specific resolution
        /// </summary>
        public void SetResolution(int width, int height)
        {
            _targetResolution = (width, height);
            var scaleFactor = Math.Min(
                (double)width / _config.Width,
                (double)height / _config.Height
            );
            
            ApplyScaleFactor(Math.Clamp(scaleFactor, 0.3, 2.0));
        }

        /// <summary>
        /// Sets resolution using a preset
        /// </summary>
        public void SetResolutionPreset(VideoQuality quality)
        {
            var preset = ResolutionPresets.GetPreset(quality);
            SetResolution(preset.Width, preset.Height);
        }

        /// <summary>
        /// Resets to target resolution
        /// </summary>
        public void Reset()
        {
            _targetResolution = (_config.Width, _config.Height);
            ApplyScaleFactor(_config.ScaleFactor);
            _consecutivePerformanceIssues = 0;
        }

        /// <summary>
        /// Gets scaling statistics
        /// </summary>
        public (double CurrentScale, (int Width, int Height) Current, (int Width, int Height) Target, DateTime LastAdaptation) GetStatistics()
        {
            lock (_lockObject)
            {
                return (_currentScaleFactor, _currentResolution, _targetResolution, _lastAdaptation);
            }
        }

        /// <summary>
        /// Calculates the pixel reduction percentage
        /// </summary>
        public double GetPixelReduction()
        {
            var originalPixels = _targetResolution.Width * _targetResolution.Height;
            var currentPixels = _currentResolution.Width * _currentResolution.Height;
            
            if (originalPixels == 0) return 0;
            
            return (1.0 - (double)currentPixels / originalPixels) * 100;
        }

        /// <summary>
        /// Estimates memory savings from current scaling
        /// </summary>
        public long EstimateMemorySavings()
        {
            var originalPixels = _targetResolution.Width * _targetResolution.Height;
            var currentPixels = _currentResolution.Width * _currentResolution.Height;
            var pixelDifference = originalPixels - currentPixels;
            
            // Assume 4 bytes per pixel (32-bit)
            return pixelDifference * 4;
        }

        public void Dispose()
        {
            if (_disposed) return;

            _disposed = true;
            _adaptationTimer?.Dispose();
        }
    }
}
