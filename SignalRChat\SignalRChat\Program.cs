namespace SignalRChat;

static class Program
{
    /// <summary>
    ///  The main entry point for the application.
    /// </summary>
    [STAThread]
    static void Main()
    {
        // To customize application configuration such as set high DPI settings or default font,
        // see https://aka.ms/applicationconfiguration.
        ApplicationConfiguration.Initialize();

        // Set up Dependency Injection
        var services = new ServiceCollection();

        services.InjectSignalRChatServices();
        IServiceProvider serviceProvider = services.BuildServiceProvider();

        LoginView loginView = serviceProvider.GetRequiredService<LoginView>();
        loginView.Show();
        Application.Run();
    }
}