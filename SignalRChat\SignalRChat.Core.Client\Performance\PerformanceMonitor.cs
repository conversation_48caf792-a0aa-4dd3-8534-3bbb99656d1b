using System.Diagnostics;

namespace SignalRChat.Core.Client.Performance
{
    /// <summary>
    /// Performance metrics data structure
    /// </summary>
    public class PerformanceMetrics
    {
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public double CpuUsagePercent { get; set; }
        public long MemoryUsageMB { get; set; }
        public double CurrentFrameRate { get; set; }
        public long NetworkBandwidthKbps { get; set; }
        public TimeSpan FrameProcessingTime { get; set; }
        public int QueuedFrames { get; set; }
        public int DroppedFrames { get; set; }
        public double CompressionRatio { get; set; }
        public long AverageFrameSize { get; set; }
    }

    /// <summary>
    /// Performance monitoring and metrics collection
    /// </summary>
    public class PerformanceMonitor : IDisposable
    {
        private readonly PerformanceCounter _cpuCounter;
        private readonly PerformanceCounter _memoryCounter;
        private readonly Timer _monitoringTimer;
        private readonly Queue<PerformanceMetrics> _metricsHistory;
        private readonly object _lockObject = new();
        
        private long _totalFramesProcessed;
        private long _totalFramesDropped;
        private long _totalBytesTransmitted;
        private DateTime _lastFrameTime;
        private DateTime _startTime;
        private bool _disposed;

        public event Action<PerformanceMetrics>? MetricsUpdated;

        public PerformanceMetrics CurrentMetrics { get; private set; } = new();
        public bool IsMonitoring { get; private set; }

        public PerformanceMonitor()
        {
            _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
            _memoryCounter = new PerformanceCounter("Memory", "Available MBytes");
            _metricsHistory = new Queue<PerformanceMetrics>();
            _monitoringTimer = new Timer(UpdateMetrics, null, Timeout.Infinite, Timeout.Infinite);
            _startTime = DateTime.Now;
        }

        /// <summary>
        /// Starts performance monitoring
        /// </summary>
        public void StartMonitoring(TimeSpan interval)
        {
            if (_disposed) return;

            IsMonitoring = true;
            _startTime = DateTime.Now;
            _monitoringTimer.Change(TimeSpan.Zero, interval);
        }

        /// <summary>
        /// Stops performance monitoring
        /// </summary>
        public void StopMonitoring()
        {
            IsMonitoring = false;
            _monitoringTimer.Change(Timeout.Infinite, Timeout.Infinite);
        }

        /// <summary>
        /// Records a frame processing event
        /// </summary>
        public void RecordFrameProcessed(TimeSpan processingTime, long frameSize, bool wasDropped = false)
        {
            lock (_lockObject)
            {
                _totalFramesProcessed++;
                if (wasDropped)
                    _totalFramesDropped++;

                _totalBytesTransmitted += frameSize;
                _lastFrameTime = DateTime.Now;

                CurrentMetrics.FrameProcessingTime = processingTime;
                CurrentMetrics.AverageFrameSize = _totalFramesProcessed > 0 ? 
                    _totalBytesTransmitted / _totalFramesProcessed : 0;
                CurrentMetrics.DroppedFrames = (int)_totalFramesDropped;
            }
        }

        /// <summary>
        /// Updates current metrics
        /// </summary>
        private void UpdateMetrics(object? state)
        {
            if (_disposed || !IsMonitoring) return;

            try
            {
                lock (_lockObject)
                {
                    var metrics = new PerformanceMetrics
                    {
                        Timestamp = DateTime.Now,
                        CpuUsagePercent = GetCpuUsage(),
                        MemoryUsageMB = GetMemoryUsage(),
                        CurrentFrameRate = CalculateCurrentFrameRate(),
                        NetworkBandwidthKbps = CalculateNetworkBandwidth(),
                        FrameProcessingTime = CurrentMetrics.FrameProcessingTime,
                        QueuedFrames = CurrentMetrics.QueuedFrames,
                        DroppedFrames = CurrentMetrics.DroppedFrames,
                        CompressionRatio = CalculateCompressionRatio(),
                        AverageFrameSize = CurrentMetrics.AverageFrameSize
                    };

                    CurrentMetrics = metrics;
                    
                    // Keep only last 100 metrics for history
                    _metricsHistory.Enqueue(metrics);
                    while (_metricsHistory.Count > 100)
                        _metricsHistory.Dequeue();

                    MetricsUpdated?.Invoke(metrics);
                }
            }
            catch (Exception ex)
            {
                // Log error but don't stop monitoring
                Debug.WriteLine($"Error updating performance metrics: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets current CPU usage percentage
        /// </summary>
        private double GetCpuUsage()
        {
            try
            {
                return _cpuCounter.NextValue();
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Gets current memory usage in MB
        /// </summary>
        private long GetMemoryUsage()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                return process.WorkingSet64 / (1024 * 1024);
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Calculates current frame rate
        /// </summary>
        private double CalculateCurrentFrameRate()
        {
            var elapsed = DateTime.Now - _startTime;
            return elapsed.TotalSeconds > 0 ? _totalFramesProcessed / elapsed.TotalSeconds : 0;
        }

        /// <summary>
        /// Calculates network bandwidth usage
        /// </summary>
        private long CalculateNetworkBandwidth()
        {
            var elapsed = DateTime.Now - _startTime;
            if (elapsed.TotalSeconds > 0)
            {
                var bytesPerSecond = _totalBytesTransmitted / elapsed.TotalSeconds;
                return (long)(bytesPerSecond * 8 / 1024); // Convert to Kbps
            }
            return 0;
        }

        /// <summary>
        /// Calculates compression ratio
        /// </summary>
        private double CalculateCompressionRatio()
        {
            // This would need to be calculated based on original vs compressed frame sizes
            // For now, return a placeholder value
            return 0.0;
        }

        /// <summary>
        /// Gets performance metrics history
        /// </summary>
        public IEnumerable<PerformanceMetrics> GetMetricsHistory()
        {
            lock (_lockObject)
            {
                return _metricsHistory.ToArray();
            }
        }

        /// <summary>
        /// Gets average metrics over a specified time period
        /// </summary>
        public PerformanceMetrics GetAverageMetrics(TimeSpan period)
        {
            lock (_lockObject)
            {
                var cutoff = DateTime.Now - period;
                var relevantMetrics = _metricsHistory.Where(m => m.Timestamp >= cutoff).ToList();
                
                if (!relevantMetrics.Any())
                    return new PerformanceMetrics();

                return new PerformanceMetrics
                {
                    Timestamp = DateTime.Now,
                    CpuUsagePercent = relevantMetrics.Average(m => m.CpuUsagePercent),
                    MemoryUsageMB = (long)relevantMetrics.Average(m => m.MemoryUsageMB),
                    CurrentFrameRate = relevantMetrics.Average(m => m.CurrentFrameRate),
                    NetworkBandwidthKbps = (long)relevantMetrics.Average(m => m.NetworkBandwidthKbps),
                    FrameProcessingTime = TimeSpan.FromMilliseconds(
                        relevantMetrics.Average(m => m.FrameProcessingTime.TotalMilliseconds)),
                    QueuedFrames = (int)relevantMetrics.Average(m => m.QueuedFrames),
                    DroppedFrames = relevantMetrics.Max(m => m.DroppedFrames),
                    CompressionRatio = relevantMetrics.Average(m => m.CompressionRatio),
                    AverageFrameSize = (long)relevantMetrics.Average(m => m.AverageFrameSize)
                };
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            _disposed = true;
            StopMonitoring();
            _monitoringTimer?.Dispose();
            _cpuCounter?.Dispose();
            _memoryCounter?.Dispose();
        }
    }
}
