﻿namespace SignalRChat.Core.Client.Infrastructure
{
    public class Session : ISession
    {
        private UserDto _user;
        public UserDto User
        {
            get { return _user; }
            set
            {
                _user = value;
            }
        }

        private BindingList<ChatCard> _chatCards = [];
        public BindingList<ChatCard> ChatCards
        {
            get { return _chatCards; }
            set
            {
                _chatCards = value;
            }
        }

        private ChatCard _selectedChatCard;
        public ChatCard SelectedChatCard
        {
            get { return _selectedChatCard; }
            set
            {
                _selectedChatCard = value;
                if (SelectedChatCard.HasSentNewMessage) SelectedChatCard.HasSentNewMessage = false;
            }
        }

        public ChatCard? GetChatCard(Ulid clientId) => ChatCards.SingleOrDefault(x => x.ClientId.Equals(clientId));
    }
}
