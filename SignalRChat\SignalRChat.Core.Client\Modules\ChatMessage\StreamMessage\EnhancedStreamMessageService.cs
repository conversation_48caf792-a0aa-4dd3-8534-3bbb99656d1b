using SignalRChat.Core.Client.Configuration;
using SignalRChat.Core.Client.Helpers;
using SignalRChat.Core.Client.Memory;
using SignalRChat.Core.Client.Network;
using SignalRChat.Core.Client.Performance;
using SignalRChat.Core.Client.Streaming;
using System.Diagnostics;

namespace SignalRChat.Core.Client.Modules.ChatMessage.StreamMessage
{
    /// <summary>
    /// Enhanced streaming service with performance optimization and adaptive quality
    /// </summary>
    public class EnhancedStreamMessageService : BaseChatMessage, IStreamMessageService
    {
        private readonly SemaphoreSlim _streamingLock = new(1);
        private readonly StreamingConfiguration _config;
        private readonly PerformanceMonitor _performanceMonitor;
        private readonly BandwidthMonitor _bandwidthMonitor;
        private readonly AdaptiveFrameRateController _frameRateController;
        private readonly BitmapMemoryPool _bitmapPool;
        private readonly ByteArrayPool _byteArrayPool;
        private readonly Timer _streamingTimer;
        
        private CancellationTokenSource? _streamingCancellation;
        private Task? _streamingTask;
        private DateTime _lastFrameTime;
        private int _frameCount;
        private bool _disposed;

        public event StreamMessageReceivedEventHandler? OnNewStreamMessage;
        public bool IsStreaming { get; private set; }

        // Performance metrics
        public PerformanceMetrics CurrentPerformance => _performanceMonitor.CurrentMetrics;
        public BandwidthStatistics CurrentBandwidth => _bandwidthMonitor.CurrentStatistics;
        public int CurrentFrameRate => _frameRateController.CurrentFrameRate;

        public EnhancedStreamMessageService(
            ISignalRConnectionManager signalRConnectionManager,
            ISendChatMessageManager sendChatMessageManager,
            IReceiveChatMessageManager receiveChatMessageManager,
            ISession session,
            ILogger logger,
            StreamingConfiguration? config = null) 
            : base(signalRConnectionManager, sendChatMessageManager, receiveChatMessageManager, session, logger)
        {
            _config = config ?? StreamingConfiguration.GetPreset(VideoQuality.Medium);
            _config.Validate();

            _performanceMonitor = new PerformanceMonitor();
            _bandwidthMonitor = new BandwidthMonitor();
            _frameRateController = new AdaptiveFrameRateController(_config, _performanceMonitor);
            _bitmapPool = new BitmapMemoryPool(10, _config.Width, _config.Height);
            _byteArrayPool = new ByteArrayPool(_config.Width * _config.Height * 4);

            _streamingTimer = new Timer(StreamFrame, null, Timeout.Infinite, Timeout.Infinite);

            _signalRConnectionManager.OnStreamMessageReceived += Receive;
            
            // Subscribe to adaptive events
            _frameRateController.FrameRateChanged += OnFrameRateChanged;
            _bandwidthMonitor.StatisticsUpdated += OnBandwidthStatsUpdated;
        }

        /// <summary>
        /// Starts streaming with enhanced optimization
        /// </summary>
        public async Task<bool> SendAsync()
        {
            if (IsStreaming) return false;

            await _streamingLock.WaitAsync();
            try
            {
                IsStreaming = true;
                _streamingCancellation = new CancellationTokenSource();
                _lastFrameTime = DateTime.Now;
                _frameCount = 0;

                // Start monitoring
                _performanceMonitor.StartMonitoring(TimeSpan.FromSeconds(1));
                _bandwidthMonitor.StartMonitoring(TimeSpan.FromSeconds(2));

                // Create initial stream message
                var request = new StreamMessageRequest(
                    UlidGenerator.Generator(),
                    _session.User.ClientId,
                    _session.SelectedChatCard.ClientId,
                    UlidGenerator.Generator()
                );

                var streamChatMessageDto = new StreamChatMessageDto(request);
                _session.SelectedChatCard.ChatMessages.Add(streamChatMessageDto);

                // Start streaming task
                _streamingTask = StreamingLoop(request, _streamingCancellation.Token);

                return true;
            }
            finally
            {
                _streamingLock.Release();
            }
        }

        /// <summary>
        /// Main streaming loop with adaptive optimization
        /// </summary>
        private async Task StreamingLoop(StreamMessageRequest request, CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested && IsStreaming)
                {
                    var frameStart = DateTime.Now;
                    var frameInterval = _frameRateController.GetFrameIntervalMs();

                    try
                    {
                        await ProcessAndSendFrame(request, cancellationToken);
                        _frameCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger.Error(ex, $"[{nameof(EnhancedStreamMessageService)}] Frame processing error");
                        
                        // Record failed frame
                        _performanceMonitor.RecordFrameProcessed(DateTime.Now - frameStart, 0, true);
                        continue;
                    }

                    // Adaptive delay based on current frame rate
                    var processingTime = DateTime.Now - frameStart;
                    var remainingTime = frameInterval - (int)processingTime.TotalMilliseconds;
                    
                    if (remainingTime > 0)
                    {
                        await Task.Delay(remainingTime, cancellationToken);
                    }

                    _lastFrameTime = DateTime.Now;
                }
            }
            catch (OperationCanceledException)
            {
                // Expected when stopping
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"[{nameof(EnhancedStreamMessageService)}] Streaming loop error");
            }
        }

        /// <summary>
        /// Processes and sends a single frame
        /// </summary>
        private async Task ProcessAndSendFrame(StreamMessageRequest request, CancellationToken cancellationToken)
        {
            var frameStart = DateTime.Now;
            PooledBitmap? pooledBitmap = null;
            byte[]? frameData = null;

            try
            {
                // Capture screen
                using var screenshot = ScreenCapture.CaptureDesktop();
                if (screenshot == null) return;

                // Get pooled bitmap for processing
                pooledBitmap = _bitmapPool.Rent(screenshot.Width, screenshot.Height);
                pooledBitmap.CopyFrom(screenshot);

                // Apply optimizations based on current configuration
                var optimizedConfig = GetOptimizedConfiguration();
                
                // Process and compress image
                frameData = EnhancedImageProcessor.ProcessImage(pooledBitmap.Bitmap, optimizedConfig);
                
                if (frameData?.Length > 0)
                {
                    request.Stream = frameData;

                    // Record transmission start
                    var transmissionStart = DateTime.Now;
                    
                    // Send frame (fire and forget for performance)
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            var success = await _sendChatMessageManager.SendAsync(request);
                            var transmissionTime = DateTime.Now - transmissionStart;
                            
                            // Record bandwidth usage
                            _bandwidthMonitor.RecordTransmission(frameData.Length, transmissionTime, success);
                        }
                        catch (Exception ex)
                        {
                            _logger.Error(ex, "Frame transmission error");
                            _bandwidthMonitor.RecordTransmission(frameData.Length, DateTime.Now - transmissionStart, false);
                        }
                    }, cancellationToken);
                }

                // Record performance metrics
                var processingTime = DateTime.Now - frameStart;
                _performanceMonitor.RecordFrameProcessed(processingTime, frameData?.Length ?? 0);
            }
            finally
            {
                pooledBitmap?.Dispose();
                
                // Return byte array to pool if using pooled arrays
                if (frameData != null && frameData.Length == _byteArrayPool._arraySize)
                {
                    _byteArrayPool.Return(frameData);
                }
            }
        }

        /// <summary>
        /// Gets optimized configuration based on current performance
        /// </summary>
        private StreamingConfiguration GetOptimizedConfiguration()
        {
            var optimized = _config.Clone();

            // Apply bandwidth-based optimizations
            if (_config.AdaptiveBandwidth)
            {
                optimized = _bandwidthMonitor.SuggestOptimalConfiguration(optimized);
            }

            // Apply performance-based optimizations
            var metrics = _performanceMonitor.CurrentMetrics;
            if (metrics.CpuUsagePercent > _config.MaxCpuUsagePercent * 0.9)
            {
                // Reduce quality to save CPU
                optimized.JpegQuality = Math.Max(50, optimized.JpegQuality - 10);
                optimized.ScaleFactor = Math.Max(0.5, optimized.ScaleFactor - 0.1);
            }

            if (metrics.MemoryUsageMB > _config.MaxMemoryUsageMB * 0.9)
            {
                // Reduce resolution to save memory
                optimized.ScaleFactor = Math.Max(0.5, optimized.ScaleFactor - 0.1);
            }

            return optimized;
        }

        /// <summary>
        /// Handles frame rate changes
        /// </summary>
        private void OnFrameRateChanged(int newFrameRate)
        {
            _logger.Information($"Frame rate adapted to {newFrameRate} FPS");
        }

        /// <summary>
        /// Handles bandwidth statistics updates
        /// </summary>
        private void OnBandwidthStatsUpdated(BandwidthStatistics stats)
        {
            if (stats.PacketLossPercent > 10)
            {
                _logger.Warning($"High packet loss detected: {stats.PacketLossPercent:F1}%");
            }
        }

        /// <summary>
        /// Stops streaming
        /// </summary>
        public async Task StopStreaming()
        {
            if (!IsStreaming) return;

            await _streamingLock.WaitAsync();
            try
            {
                IsStreaming = false;
                _streamingCancellation?.Cancel();

                if (_streamingTask != null)
                {
                    await _streamingTask;
                    _streamingTask = null;
                }

                _streamingCancellation?.Dispose();
                _streamingCancellation = null;

                // Stop monitoring
                _performanceMonitor.StopMonitoring();
                _bandwidthMonitor.StopMonitoring();

                _logger.Information($"Streaming stopped. Total frames: {_frameCount}");
            }
            finally
            {
                _streamingLock.Release();
            }
        }

        /// <summary>
        /// Updates streaming configuration
        /// </summary>
        public void UpdateConfiguration(StreamingConfiguration newConfig)
        {
            newConfig.Validate();
            
            // Update configuration properties
            _config.TargetFrameRate = newConfig.TargetFrameRate;
            _config.MinFrameRate = newConfig.MinFrameRate;
            _config.MaxFrameRate = newConfig.MaxFrameRate;
            _config.Width = newConfig.Width;
            _config.Height = newConfig.Height;
            _config.Quality = newConfig.Quality;
            _config.JpegQuality = newConfig.JpegQuality;
            _config.CompressionType = newConfig.CompressionType;
            _config.MaxCpuUsagePercent = newConfig.MaxCpuUsagePercent;
            _config.MaxMemoryUsageMB = newConfig.MaxMemoryUsageMB;
            _config.MaxBandwidthKbps = newConfig.MaxBandwidthKbps;

            // Reset frame rate controller
            _frameRateController.Reset();
        }

        /// <summary>
        /// Gets current streaming statistics
        /// </summary>
        public (PerformanceMetrics Performance, BandwidthStatistics Bandwidth, int FrameRate, int TotalFrames) GetStatistics()
        {
            return (_performanceMonitor.CurrentMetrics, _bandwidthMonitor.CurrentStatistics, 
                   _frameRateController.CurrentFrameRate, _frameCount);
        }

        // Implement interface methods
        public bool CanSend(StreamMessageRequest? request) => base.CanSend(request);

        public async Task<bool> SendAsync(StreamMessageRequest request) => 
            await _sendChatMessageManager.SendAsync(request);

        public async void Receive(StreamMessageResponse? response)
        {
            if (response?.Stream?.Length > 0)
            {
                OnNewStreamMessage?.Invoke(response);
                await _receiveChatMessageManager.ReceiveAsync(response);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _disposed = true;
                
                StopStreaming().Wait(5000);
                
                _streamingLock?.Dispose();
                _performanceMonitor?.Dispose();
                _bandwidthMonitor?.Dispose();
                _frameRateController?.Dispose();
                _bitmapPool?.Dispose();
                _byteArrayPool?.Dispose();
                _streamingTimer?.Dispose();
                _streamingCancellation?.Dispose();
            }
            
            base.Dispose(disposing);
        }
    }
}
