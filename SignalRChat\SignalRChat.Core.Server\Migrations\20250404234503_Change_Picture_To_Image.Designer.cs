﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using SignalRChat.Core.Server.Data;


#nullable disable

namespace SignalRChat.Core.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250404234503_Change_Picture_To_Image")]
    partial class Change_Picture_To_Image
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.12")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            MySqlModelBuilderExtensions.AutoIncrementColumns(modelBuilder);

            modelBuilder.Entity("SignalRChat.Core.Models.ChatMessage", b =>
                {
                    b.Property<byte[]>("MessageId")
                        .HasColumnType("varbinary(16)");

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(21)
                        .HasColumnType("varchar(21)");

                    b.Property<byte>("MessageType")
                        .HasColumnType("tinyint unsigned");

                    b.Property<byte[]>("ReceiverId")
                        .IsRequired()
                        .HasColumnType("varbinary(16)");

                    b.Property<byte[]>("SenderId")
                        .IsRequired()
                        .HasColumnType("varbinary(16)");

                    b.HasKey("MessageId");

                    b.HasIndex("ReceiverId");

                    b.HasIndex("SenderId");

                    b.ToTable("ChatMessages");

                    b.HasDiscriminator().HasValue("ChatMessage");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("SignalRChat.Core.Models.Client", b =>
                {
                    b.Property<byte[]>("ClientId")
                        .HasColumnType("varbinary(16)");

                    b.Property<string>("ChatType")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("ConnectionId")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<byte[]>("Photo")
                        .IsRequired()
                        .HasColumnType("longblob");

                    b.HasKey("ClientId");

                    b.ToTable("Clients");

                    b.HasDiscriminator().HasValue("Client");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("SignalRChat.Core.Models.ImageChatMessage", b =>
                {
                    b.HasBaseType("SignalRChat.Core.Models.ChatMessage");

                    b.Property<byte[]>("Image")
                        .IsRequired()
                        .HasColumnType("longblob");

                    b.HasDiscriminator().HasValue("ImageChatMessage");
                });

            modelBuilder.Entity("SignalRChat.Core.Models.RecordChatMessage", b =>
                {
                    b.HasBaseType("SignalRChat.Core.Models.ChatMessage");

                    b.Property<byte[]>("Record")
                        .IsRequired()
                        .HasColumnType("longblob");

                    b.HasDiscriminator().HasValue("RecordChatMessage");
                });

            modelBuilder.Entity("SignalRChat.Core.Models.TextChatMessage", b =>
                {
                    b.HasBaseType("SignalRChat.Core.Models.ChatMessage");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.HasDiscriminator().HasValue("TextChatMessage");
                });

            modelBuilder.Entity("SignalRChat.Core.Models.Group", b =>
                {
                    b.HasBaseType("SignalRChat.Core.Models.Client");

                    b.HasDiscriminator().HasValue("Group");
                });

            modelBuilder.Entity("SignalRChat.Core.Models.User", b =>
                {
                    b.HasBaseType("SignalRChat.Core.Models.Client");

                    b.Property<byte[]>("GroupClientId")
                        .HasColumnType("varbinary(16)");

                    b.HasIndex("GroupClientId");

                    b.HasDiscriminator().HasValue("User");
                });

            modelBuilder.Entity("SignalRChat.Core.Models.ChatMessage", b =>
                {
                    b.HasOne("SignalRChat.Core.Models.Client", "Receiver")
                        .WithMany("ReceivedMessages")
                        .HasForeignKey("ReceiverId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SignalRChat.Core.Models.Client", "Sender")
                        .WithMany("SentMessages")
                        .HasForeignKey("SenderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Receiver");

                    b.Navigation("Sender");
                });

            modelBuilder.Entity("SignalRChat.Core.Models.User", b =>
                {
                    b.HasOne("SignalRChat.Core.Models.Group", null)
                        .WithMany("Users")
                        .HasForeignKey("GroupClientId");
                });

            modelBuilder.Entity("SignalRChat.Core.Models.Client", b =>
                {
                    b.Navigation("ReceivedMessages");

                    b.Navigation("SentMessages");
                });

            modelBuilder.Entity("SignalRChat.Core.Models.Group", b =>
                {
                    b.Navigation("Users");
                });
#pragma warning restore 612, 618
        }
    }
}
