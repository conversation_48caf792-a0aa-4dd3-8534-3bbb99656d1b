﻿namespace SignalRChat.Views
{
    public partial class ChatView : BaseView, IDisposable
    {
        private readonly ISession _session;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogoutService _logoutService;
        private readonly IBuzzMessageService _buzzMessageService;
        private readonly ICreateGroupService _createGroupService;
        private readonly IJoinGroupService _joinGroupService;
        private readonly ISignalRConnectionManager _signalRConnectionManager;

        public ChatView(ISession session, IServiceProvider serviceProvider)
        {
            _session = session ?? throw new ArgumentNullException(nameof(session));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logoutService = serviceProvider.GetRequiredService<ILogoutService>();
            _buzzMessageService = serviceProvider.GetRequiredService<IBuzzMessageService>();
            _createGroupService = serviceProvider.GetRequiredService<ICreateGroupService>();
            _joinGroupService = serviceProvider.GetRequiredService<IJoinGroupService>();
            _signalRConnectionManager = serviceProvider.GetRequiredService<ISignalRConnectionManager>();

            _buzzMessageService.OnBuzzMessage += buzzMessageService_OnBuzzMessageAsync;

            _signalRConnectionManager.OnConnectionReconnected += () => UpdateHeaderState();
            _signalRConnectionManager.OnConnectionReconnecting += () => UpdateHeaderState();
            _signalRConnectionManager.OnConnectionClosed += () => UpdateHeaderState();

            InitializeComponent();
            contactCardList.DataSource = _session.ChatCards;

            btnCreateGroup.Click += BtnCreateGroup_Click;
            btnJoinGroup.Click += BtnJoinGroup_Click;
            //this.FormClosing += async (s, e) => await _logoutService.Logout();

            contactCardList.ContactCardSelected += ContactCardList_ContactCardSelected;

            this.Load += (s, e) => UpdateHeaderState();
        }

        private void UpdateHeaderState()
        {
            this.SafelyInvokeAction(() =>
            {
                lblHeaderName.Text = string.Format("Welcome {0}!", _session.User.Name);
                if (_signalRConnectionManager.IsConnected)
                    lblHeaderState.Text = "Connected";
                else
                    lblHeaderState.Text = "Disconnected";
            });
        }

        private async void BtnCreateGroup_Click(object? sender, EventArgs e)
        {
            await CreateGroup();
        }

        private async void BtnJoinGroup_Click(object? sender, EventArgs e)
        {
            await JoinGroup();
        }

        private void ContactCardList_ContactCardSelected(ContactCard card)
        {
            _session.SelectedChatCard = card.ChatCard;
            Control? control = this.tlpMain.GetControlFromPosition(1, 1);
            if (control != null)
                this.tlpMain.Controls.Remove(control);
            this.tlpMain.Controls.Add(card.Chat, 1, 1);
            this.tlpMain.SetRowSpan(card.Chat, 2);
        }

        private async Task CreateGroup()
        {
            using var createGroupView = new CreateGroupView();
            if (createGroupView.ShowDialog() == DialogResult.OK)
            {
                string name = createGroupView.GroupName;
                byte[] photo = createGroupView.GroupPhoto;
                await _createGroupService.SendAsync(name, photo);
            }
        }

        private async Task JoinGroup()
        {
            using var joinGroupView = new JoinGroupView();
            if (joinGroupView.ShowDialog() == DialogResult.OK)
            {
                Ulid groupId = joinGroupView.GroupId;
                await _joinGroupService.SendAsync(groupId);
            }
        }

        private async void buzzMessageService_OnBuzzMessageAsync(BuzzMessageResponse? response)
        {
            if (response is null)
                return;
            SystemSounds.Hand.Play();
            await ShakeScreen(100, 10); // Shake the screen for 100 ms with an amplitude of 10 pixels
        }

        private async Task ShakeScreen(int duration, int amplitude)
        {
            if (WindowState == FormWindowState.Minimized)
                WindowState = FormWindowState.Normal;
            this.SafelyInvokeAction(() => Activate());
            this.SafelyInvokeAction(() => BringToFront());

            var originalLocation = Location;
            var rnd = new Random();

            for (int i = 0; i < duration; i++)
            {
                var offsetX = rnd.Next(-amplitude, amplitude);
                var offsetY = rnd.Next(-amplitude, amplitude);
                this.SafelyInvokeAction(() => Location = new Point(originalLocation.X + offsetX, originalLocation.Y + offsetY));
                await Task.Delay(10);
            }

            this.SafelyInvokeAction(() => Location = originalLocation);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Unsubscribe from events
                _buzzMessageService.OnBuzzMessage -= buzzMessageService_OnBuzzMessageAsync;
                btnCreateGroup.Click -= BtnCreateGroup_Click;
                btnJoinGroup.Click -= BtnJoinGroup_Click;
                contactCardList.ContactCardSelected -= ContactCardList_ContactCardSelected;

                // Dispose managed resources
                components?.Dispose();
            }

            if (disposing && (components != null))
            {
                components.Dispose();
            }

            base.Dispose(disposing);
        }
    }
}
