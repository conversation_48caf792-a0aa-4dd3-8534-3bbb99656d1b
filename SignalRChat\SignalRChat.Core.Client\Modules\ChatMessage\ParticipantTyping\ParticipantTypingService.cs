﻿namespace SignalRChat.Core.Client.Modules.ChatMessage.ParticipantTyping
{
    public delegate void ParticipantTypingRequestedEventHandler(ParticipantTypingRequest? request);
    public delegate void ParticipantTypingReceivedEventHandler(ParticipantTypingResponse? response);

    public interface IParticipantTypingService : IBaseModuleService<ParticipantTypingRequest, ParticipantTypingResponse>
    {
        event ParticipantTypingReceivedEventHandler OnParticipantTyping;
        Task<bool> SendAsync(Ulid recipientId);
    }

    public class ParticipantTypingService : BaseChatMessage, IParticipantTypingService
    {
        public event ParticipantTypingReceivedEventHandler OnParticipantTyping;

        public ParticipantTypingService(
            ISignalRConnectionManager signalRConnectionManager,
            ISendChatMessageManager sendChatMessageManager,
            IReceiveChatMessageManager receiveChatMessageManager,
            ISession session,
            ILogger logger) : base(signalRConnectionManager, sendChatMessageManager, receiveChatMessageManager, session, logger)
        {
            _signalRConnectionManager.OnParticipantTypingReceived += Receive;
        }

        public async Task<bool> SendAsync(Ulid recipientId)
        {
            var request = new ParticipantTypingRequest(
                UlidGenerator.Generator(),
                _session.User.ClientId,
                recipientId
            );

            if (CanSend(request))
                return await SendAsync(request);
            return false;
        }

        public bool CanSend(ParticipantTypingRequest? request)
        {
            return base.CanSend(request);
        }

        public async Task<bool> SendAsync(ParticipantTypingRequest request)
        {
            return await _sendChatMessageManager.SendAsync(request);
        }

        public async void Receive(ParticipantTypingResponse? response)
        {
            if (response is null)
                return;

            await _receiveChatMessageManager.ReceiveAsync(response);
            OnParticipantTyping?.Invoke(response);
        }
    }
}
