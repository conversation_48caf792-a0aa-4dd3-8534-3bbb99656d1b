﻿namespace SignalRChat.Core.Client.Strategies.Messages.StreamMessage
{
    public class StreamMessageDisplayStrategy : IMessageDisplayStrategy
    {
        private IStreamMessageView _view;

        public StreamMessageDisplayStrategy(IStreamMessageView view)
        {
            _view = view;
        }

        public void DisplayMessage(IMessageContainer tlpMain, ChatMessageDto chatMessageDto)
        {
            if (chatMessageDto != null && chatMessageDto is StreamChatMessageDto streamChatMessageDto)
            {
                _view.Size = new Size(150, 150);
                _view.Image = Converters.ByteArrayToImage(streamChatMessageDto.Stream);
            }

            tlpMain.Add(_view, 0, 0);
        }

        public int CalculateHeight()
        {
            if (_view is null)
                return default;

            int imageMessageHeight = _view.Height + 10;
            return imageMessageHeight;
        }

        public void SetBackColor(Color color) => _view.BackColor = color;
    }
}
