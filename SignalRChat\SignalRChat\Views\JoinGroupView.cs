﻿namespace SignalRChat.Views
{
    public partial class JoinGroupView : Form, IDisposable
    {
        public Ulid GroupId { get; private set; } = Ulid.Empty;

        public JoinGroupView()
        {
            InitializeComponent();
            btnJoin.Click += BtnJoin_Click;
        }

        private void BtnJoin_Click(object? sender, EventArgs e)
        {
            if (Ulid.TryParse(txtGroupName.Text, out Ulid ulid))
            {
                GroupId = ulid;
                DialogResult = DialogResult.OK;
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Unsubscribe from events
                btnJoin.Click -= BtnJoin_Click;

                // Dispose managed resources
                components?.Dispose();
            }

            if (disposing && (components != null))
            {
                components.Dispose();
            }

            base.Dispose(disposing);
        }
    }
}
