﻿namespace SignalRChat.Core.Modules.ChatMessage.ImageMessage
{
    public record ImageMessageRequest(Ulid MessageId, Ulid SenderId, Ulid ReceiverId, byte[] Image) : BaseChatMessageRequest(MessageId, SenderId, ReceiverId)
    {
        public override bool IsValid()
        {
            return
                base.IsValid()
                && Image != null
                && Image.Length != 0;
        }
    }

    public record ImageMessageResponse(Ulid MessageId, Ulid SenderId, Ulid ReceiverId, byte[] Image) : BaseChatMessageResponse(MessageId, SenderId, ReceiverId, true);
}
