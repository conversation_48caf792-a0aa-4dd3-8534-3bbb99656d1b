﻿global using Microsoft.Extensions.DependencyInjection;
global using SignalRChat.Core.Client.DI;
global using SignalRChat.Core.Client.Helpers;
global using SignalRChat.Core.Client.Infrastructure;
global using SignalRChat.Core.Client.Infrastructure.ConnectionManager;
global using SignalRChat.Core.Client.Modules.Authentication.Login;
global using SignalRChat.Core.Client.Modules.Authentication.Logout;
global using SignalRChat.Core.Client.Modules.ChatMessage.BuzzMessage;
global using SignalRChat.Core.Client.Modules.ChatMessage.ImageMessage;
global using SignalRChat.Core.Client.Modules.ChatMessage.ParticipantTyping;
global using SignalRChat.Core.Client.Modules.ChatMessage.RecordMessage;
global using SignalRChat.Core.Client.Modules.ChatMessage.StreamMessage;
global using SignalRChat.Core.Client.Modules.ChatMessage.TextMessage;
global using SignalRChat.Core.Client.Modules.Group.CreateGroup;
global using SignalRChat.Core.Client.Modules.Group.JoinGroup;
global using SignalRChat.Core.Client.Modules.Group.LeaveGroup;
global using SignalRChat.Core.Client.Strategies.Messages;
global using SignalRChat.Core.Client.Strategies.Messages.ImageMessage;
global using SignalRChat.Core.Client.Strategies.Messages.RecordMessage;
global using SignalRChat.Core.Client.Strategies.Messages.StreamMessage;
global using SignalRChat.Core.Client.Strategies.Messages.TextMessage;
global using SignalRChat.Core.Enums;
global using SignalRChat.Core.Helpers;
global using SignalRChat.Core.Models;
global using SignalRChat.Core.Modules.ChatMessage.BuzzMessage;
global using SignalRChat.Core.ViewModels;
global using SignalRChat.CustomControls;
global using SignalRChat.DI;
global using SignalRChat.Extensions;
global using SignalRChat.Factories.Messages;
global using SignalRChat.Helpers;
global using SignalRChat.Strategies.Messages;
global using SignalRChat.Views;
global using System.ComponentModel;
global using System.Diagnostics;
global using System.Diagnostics.CodeAnalysis;
global using System.Drawing;
global using System.Drawing.Drawing2D;
global using System.Drawing.Text;
global using System.Media;
global using System.Runtime.CompilerServices;
