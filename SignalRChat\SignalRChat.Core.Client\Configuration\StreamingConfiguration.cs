namespace SignalRChat.Core.Client.Configuration
{
    /// <summary>
    /// Enumeration for video quality presets
    /// </summary>
    public enum VideoQuality
    {
        Low = 1,
        Medium = 2,
        High = 3,
        Ultra = 4,
        Custom = 5
    }

    /// <summary>
    /// Enumeration for compression algorithms
    /// </summary>
    public enum CompressionType
    {
        None = 0,
        Jpeg = 1,
        Png = 2,
        WebP = 3,
        Auto = 4
    }

    /// <summary>
    /// Enumeration for performance modes
    /// </summary>
    public enum PerformanceMode
    {
        PowerSaver = 1,
        Balanced = 2,
        Performance = 3,
        MaxPerformance = 4
    }

    /// <summary>
    /// Configuration class for streaming parameters
    /// </summary>
    public class StreamingConfiguration
    {
        // Framerate settings
        public int TargetFrameRate { get; set; } = 15;
        public int MinFrameRate { get; set; } = 5;
        public int MaxFrameRate { get; set; } = 30;
        public bool AdaptiveFrameRate { get; set; } = true;

        // Resolution settings
        public int Width { get; set; } = 1920;
        public int Height { get; set; } = 1080;
        public bool AdaptiveResolution { get; set; } = true;
        public double ScaleFactor { get; set; } = 1.0;

        // Quality settings
        public VideoQuality Quality { get; set; } = VideoQuality.Medium;
        public int JpegQuality { get; set; } = 75;
        public CompressionType CompressionType { get; set; } = CompressionType.Auto;

        // Performance settings
        public PerformanceMode PerformanceMode { get; set; } = PerformanceMode.Balanced;
        public int MaxCpuUsagePercent { get; set; } = 70;
        public long MaxMemoryUsageMB { get; set; } = 512;
        public bool EnableMemoryPooling { get; set; } = true;

        // Network settings
        public int MaxBandwidthKbps { get; set; } = 5000;
        public bool AdaptiveBandwidth { get; set; } = true;
        public int NetworkBufferSize { get; set; } = 8192;

        // Advanced settings
        public bool EnableMultithreading { get; set; } = true;
        public int ThreadPoolSize { get; set; } = Environment.ProcessorCount;
        public bool EnableGpuAcceleration { get; set; } = false;
        public TimeSpan FrameTimeout { get; set; } = TimeSpan.FromMilliseconds(100);

        /// <summary>
        /// Gets predefined configuration for the specified quality level
        /// </summary>
        public static StreamingConfiguration GetPreset(VideoQuality quality)
        {
            return quality switch
            {
                VideoQuality.Low => new StreamingConfiguration
                {
                    TargetFrameRate = 10,
                    Width = 640,
                    Height = 480,
                    JpegQuality = 50,
                    MaxBandwidthKbps = 1000,
                    Quality = VideoQuality.Low
                },
                VideoQuality.Medium => new StreamingConfiguration
                {
                    TargetFrameRate = 15,
                    Width = 1280,
                    Height = 720,
                    JpegQuality = 75,
                    MaxBandwidthKbps = 3000,
                    Quality = VideoQuality.Medium
                },
                VideoQuality.High => new StreamingConfiguration
                {
                    TargetFrameRate = 24,
                    Width = 1920,
                    Height = 1080,
                    JpegQuality = 85,
                    MaxBandwidthKbps = 5000,
                    Quality = VideoQuality.High
                },
                VideoQuality.Ultra => new StreamingConfiguration
                {
                    TargetFrameRate = 30,
                    Width = 2560,
                    Height = 1440,
                    JpegQuality = 95,
                    MaxBandwidthKbps = 10000,
                    Quality = VideoQuality.Ultra
                },
                _ => new StreamingConfiguration()
            };
        }

        /// <summary>
        /// Validates the configuration and adjusts invalid values
        /// </summary>
        public void Validate()
        {
            // Validate framerate
            TargetFrameRate = Math.Clamp(TargetFrameRate, 1, 60);
            MinFrameRate = Math.Clamp(MinFrameRate, 1, TargetFrameRate);
            MaxFrameRate = Math.Clamp(MaxFrameRate, TargetFrameRate, 60);

            // Validate resolution
            Width = Math.Clamp(Width, 320, 3840);
            Height = Math.Clamp(Height, 240, 2160);
            ScaleFactor = Math.Clamp(ScaleFactor, 0.1, 2.0);

            // Validate quality
            JpegQuality = Math.Clamp(JpegQuality, 10, 100);

            // Validate performance
            MaxCpuUsagePercent = Math.Clamp(MaxCpuUsagePercent, 10, 100);
            MaxMemoryUsageMB = Math.Clamp(MaxMemoryUsageMB, 64, 4096);

            // Validate network
            MaxBandwidthKbps = Math.Clamp(MaxBandwidthKbps, 100, 50000);
            NetworkBufferSize = Math.Clamp(NetworkBufferSize, 1024, 65536);

            // Validate threading
            ThreadPoolSize = Math.Clamp(ThreadPoolSize, 1, Environment.ProcessorCount * 2);
        }

        /// <summary>
        /// Creates a copy of the current configuration
        /// </summary>
        public StreamingConfiguration Clone()
        {
            return new StreamingConfiguration
            {
                TargetFrameRate = TargetFrameRate,
                MinFrameRate = MinFrameRate,
                MaxFrameRate = MaxFrameRate,
                AdaptiveFrameRate = AdaptiveFrameRate,
                Width = Width,
                Height = Height,
                AdaptiveResolution = AdaptiveResolution,
                ScaleFactor = ScaleFactor,
                Quality = Quality,
                JpegQuality = JpegQuality,
                CompressionType = CompressionType,
                PerformanceMode = PerformanceMode,
                MaxCpuUsagePercent = MaxCpuUsagePercent,
                MaxMemoryUsageMB = MaxMemoryUsageMB,
                EnableMemoryPooling = EnableMemoryPooling,
                MaxBandwidthKbps = MaxBandwidthKbps,
                AdaptiveBandwidth = AdaptiveBandwidth,
                NetworkBufferSize = NetworkBufferSize,
                EnableMultithreading = EnableMultithreading,
                ThreadPoolSize = ThreadPoolSize,
                EnableGpuAcceleration = EnableGpuAcceleration,
                FrameTimeout = FrameTimeout
            };
        }
    }
}
