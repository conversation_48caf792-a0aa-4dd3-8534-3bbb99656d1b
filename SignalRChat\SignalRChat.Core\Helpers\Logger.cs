﻿namespace SignalRChat.Core.Helpers
{
    public static class Logger
    {
        public static ILogger CreateSerilog(string? logPath = null)
        {
            if (string.IsNullOrEmpty(logPath))
            {
                string rootPath = Directory.GetCurrentDirectory();
                rootPath = Path.Combine(rootPath, "Logs");
                bool isRootPathExists = Directory.Exists(rootPath);
                if (!isRootPathExists)
                    Directory.CreateDirectory(rootPath);
                logPath = Path.Combine(rootPath, "log");
            }

            LoggerConfiguration configuration = new LoggerConfiguration()
                .MinimumLevel.Verbose()
                .MinimumLevel.Override("Microsoft", LogEventLevel.Debug)
                .Enrich.FromLogContext();
            if (Debugger.IsAttached)
            {
                configuration.WriteTo.Debug();
                configuration.WriteTo.Console();
            }
            else
            {
                configuration.WriteTo.File(logPath, rollingInterval: RollingInterval.Day);
            }

            return configuration.CreateLogger();
        }
    }
}
