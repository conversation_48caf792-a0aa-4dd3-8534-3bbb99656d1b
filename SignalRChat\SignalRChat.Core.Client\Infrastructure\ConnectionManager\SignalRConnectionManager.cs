﻿namespace SignalRChat.Core.Client.Infrastructure.ConnectionManager
{
    public class SignalRConnectionManagerOptions
    {
        public IDictionary<string, string> Headers { get; set; } = new Dictionary<string, string>();
        public string Url { get; set; }
        public Ulid UserId { get; set; }
        public string UserName { get; set; }
        public byte[] Photo { get; set; } = [];
    }

    public class SignalRRetryPolicy : IRetryPolicy
    {
        public TimeSpan? NextRetryDelay(RetryContext retryContext)
        {
            return TimeSpan.FromSeconds(5);
        }
    }

    public class SignalRConnectionManager(ISession session, ILogger logger) : ISignalRConnectionManager
    {
        public event Action OnConnectionReconnecting;
        public event Action OnConnectionReconnected;
        public event Action OnConnectionClosed;
        public event Action<UserDto> OnParticipantLoggedIn;
        public event Action<Ulid> OnParticipantLoggedOut;
        public event Action<Ulid> OnParticipantDisconnected;
        public event Action<Ulid> OnParticipantReconnected;

        public event LoginReceivedEventHandler OnLoginReceived;
        public event LogoutReceivedEventHandler OnLogoutReceived;

        public event ParticipantTypingReceivedEventHandler OnParticipantTypingReceived;
        public event TextMessageReceivedEventHandler OnTextMessageReceived;
        public event ImageMessageReceivedEventHandler OnImageMessageReceived;
        public event RecordMessageReceivedEventHandler OnRecordMessageReceived;
        public event StreamMessageReceivedEventHandler OnStreamMessageReceived;
        public event BuzzMessageReceivedEventHandler OnBuzzMessageReceived;
        public event CreateGroupReceivedEventHandler OnCreateGroupReceived;
        public event JoinGroupReceivedEventHandler OnJoinGroupReceived;
        public event LeaveGroupReceivedEventHandler OnLeaveGroupReceived;

        protected HubConnection Connection { get; private set; }
        public bool IsConnected => Connection?.State == HubConnectionState.Connected;
        public bool IsLoggedIn { get; private set; }

        public async Task Subscribe(SignalRConnectionManagerOptions signalRConnectionManagerOptions)
        {
            await InitializeConnectionAsync(signalRConnectionManagerOptions);
            if (IsConnected)
                InitialEvents();
        }

        protected virtual async Task InitializeConnectionAsync(SignalRConnectionManagerOptions signalRConnectionManagerOptions)
        {
            Connection = new HubConnectionBuilder()
                .WithUrl(signalRConnectionManagerOptions.Url, options =>
                {
                    if (signalRConnectionManagerOptions.Headers.Count > 0)
                        foreach (var header in signalRConnectionManagerOptions.Headers)
                            options.Headers.TryAdd(header.Key, header.Value);
                })
                .WithAutomaticReconnect(new SignalRRetryPolicy())
                .AddJsonProtocol(options =>
                {
                    options.PayloadSerializerOptions.PropertyNamingPolicy = null;
                    options.PayloadSerializerOptions.IgnoreReadOnlyFields = true;
                    options.PayloadSerializerOptions.IgnoreReadOnlyProperties = true;
                    options.PayloadSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
                })
                .Build();

            await Connection.StartAsync();

            logger.Information($"[{nameof(SignalRConnectionManager)}] => Connected To Server");
        }

        protected virtual void InitialEvents()
        {
            Connection.On<UserDto>(nameof(ParticipantLogin), ParticipantLogin);
            Connection.On<Ulid>(nameof(ParticipantLogout), ParticipantLogoutOrDisconnection);
            Connection.On<Ulid>(nameof(ParticipantDisconnection), ParticipantLogoutOrDisconnection);
            Connection.On<Ulid>(nameof(ParticipantReconnection), ParticipantReconnection);

            Connection.On<LoginResponse?>(nameof(Login), Login);
            Connection.On<LogoutResponse?>(nameof(Logout), Logout);

            Connection.On<ParticipantTypingResponse?>(nameof(ParticipantTyping), ParticipantTyping);
            Connection.On<TextMessageResponse?>(nameof(TextMessage), TextMessage);
            Connection.On<ImageMessageResponse?>(nameof(ImageMessage), ImageMessage);
            Connection.On<RecordMessageResponse?>(nameof(RecordMessage), RecordMessage);
            Connection.On<StreamMessageResponse?>(nameof(StreamMessage), StreamMessage);
            Connection.On<BuzzMessageResponse?>(nameof(BuzzMessage), BuzzMessage);
            Connection.On<CreateGroupResponse?>(nameof(CreateGroup), CreateGroup);
            Connection.On<JoinGroupResponse?>(nameof(JoinGroup), JoinGroup);
            Connection.On<LeaveGroupResponse?>(nameof(LeaveGroup), LeaveGroup);

            Connection.Reconnecting += Reconnecting;
            Connection.Reconnected += Reconnected;
            Connection.Closed += Disconnected;

            ServicePointManager.DefaultConnectionLimit = 10;
        }

        #region SignalR Functions
        public async Task InvokeCoreAsync(string method, params object[] args)
        {
            await Connection.InvokeCoreAsync(method, args);
        }

        public async Task<T> InvokeCoreAsync<T>(string method, params object[] args)
        {
            return await Connection.InvokeCoreAsync<T>(method, args);
        }

        private async Task Reconnecting(Exception? exception)
        {
            logger.Error(exception, $"[{nameof(SignalRConnectionManager)}].[{nameof(Reconnecting)}] => Reconnecting To Server");
            OnConnectionReconnecting?.Invoke();

            if (session.User != null)
            {
                if (!string.IsNullOrEmpty(session.User.Name))
                {
                    var request = new LoginRequest(session.User.ClientId, session.User.Name, session.User.Photo);
                    var result = await LoginAsync(request);
                    if (result != null)
                        session.User = result.User;
                }
            }
        }

        private async Task Reconnected(string? arg)
        {
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(Reconnected)}] => Reconnected To Server{(string.IsNullOrEmpty(arg) ? string.Empty : $" With args => [{arg}]")}");
            OnConnectionReconnected?.Invoke();

            if (session.User != null)
            {
                if (!string.IsNullOrEmpty(session.User.Name))
                {
                    var request = new LoginRequest(session.User.ClientId, session.User.Name, session.User.Photo);
                    var result = await LoginAsync(request);
                    if (result != null)
                        session.User = result.User;
                }
            }
        }

        private async Task Disconnected(Exception? exception)
        {
            logger.Error(exception, $"[{nameof(SignalRConnectionManager)}].[{nameof(Disconnected)}] => Disconnected From Server");
            OnConnectionClosed?.Invoke();

            IsLoggedIn = false;

            await Task.CompletedTask;
        }

        public async Task<LoginResponse?> LoginAsync(LoginRequest request)
        {
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(LoginAsync)}] => {nameof(request.Name)}: {request.Name}");
            var result = await Connection.InvokeCoreAsync<LoginResponse?>("Login", [request]);
            if (result != null)
                IsLoggedIn = true;
            else
                IsLoggedIn = false;
            return result;
        }

        public async Task LogoutAsync(LogoutRequest request)
        {
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(LogoutAsync)}]");
            await Connection.InvokeCoreAsync("Logout", [request]);
        }
        #endregion

        #region Event Handlers
        private void ParticipantLogin(UserDto user)
        {
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(ParticipantLogin)}] => {nameof(user)}: {user.Name}");
            OnParticipantLoggedIn?.Invoke(user);

            var ptp = session.GetChatCard(user.ClientId);
            if (IsLoggedIn && ptp == null)
            {
                var chatCard = new ChatCard(user);
                session.ChatCards.TryAdd(chatCard);
            }
        }

        private void ParticipantLogout(Ulid clientId) => ParticipantLogoutOrDisconnection(clientId);

        private void ParticipantDisconnection(Ulid clientId) => ParticipantLogoutOrDisconnection(clientId);

        private void ParticipantLogoutOrDisconnection(Ulid clientId)
        {
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(ParticipantLogoutOrDisconnection)}] => Start Receiving Participant Disconnection Notification {nameof(clientId)}: {clientId}");
            OnParticipantLoggedOut?.Invoke(clientId);
            OnParticipantDisconnected?.Invoke(clientId);

            var person = session.GetChatCard(clientId);
            if (person != null) person.IsLoggedIn = false;
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(ParticipantLogoutOrDisconnection)}] => End Receiving Participant Disconnection Notification {nameof(clientId)}: {clientId}");
        }

        private void ParticipantReconnection(Ulid clientId)
        {
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(ParticipantReconnection)}] => Start Receiving Participant Reconnection Notification {nameof(clientId)}: {clientId}");
            OnParticipantReconnected?.Invoke(clientId);

            var person = session.GetChatCard(clientId);
            if (person != null) person.IsLoggedIn = true;
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(ParticipantReconnection)}] => End Receiving Participant Reconnection Notification {nameof(clientId)}: {clientId}");
        }

        private void Login(LoginResponse? response)
        {
            if (response is null)
                return;
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(Login)}] => Start Login As [{response.User.Name}]");
            OnLoginReceived?.Invoke(response);
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(Login)}] => End Login As [{response.User.Name}]");
        }

        private void Logout(LogoutResponse? response)
        {
            if (response is null)
                return;
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(Logout)}] => Start Logout As [{response.Id}]");
            OnLogoutReceived?.Invoke(response);
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(Logout)}] => End Logout As [{response.Id}]");
        }

        private void ParticipantTyping(ParticipantTypingResponse? response)
        {
            if (response is null)
                return;
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(ParticipantTyping)}] => Start Receiving Text Message From [{response?.SenderId}]");
            OnParticipantTypingReceived?.Invoke(response);
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(ParticipantTyping)}] => End Receiving Text Message From [{response?.SenderId}]");
        }

        private void TextMessage(TextMessageResponse? response)
        {
            if (response is null)
                return;
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(TextMessage)}] => Start Receiving Text Message From [{response?.SenderId}]");
            OnTextMessageReceived?.Invoke(response);
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(TextMessage)}] => End Receiving Text Message From [{response?.SenderId}]");
        }

        private void ImageMessage(ImageMessageResponse? response)
        {
            if (response is null)
                return;
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(ImageMessage)}] => Start Receiving Image Message From [{response?.SenderId}]");
            OnImageMessageReceived?.Invoke(response);
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(ImageMessage)}] => End Receiving Image Message From [{response?.SenderId}]");
        }

        private void RecordMessage(RecordMessageResponse? response)
        {
            if (response is null)
                return;
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(RecordMessage)}] => Start Receiving Record Message From [{response?.SenderId}]");
            OnRecordMessageReceived?.Invoke(response);
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(RecordMessage)}] => End Receiving Record Message From [{response?.SenderId}]");
        }

        private void StreamMessage(StreamMessageResponse? response)
        {
            if (response is null)
                return;
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(StreamMessage)}] => Start Receiving Stream Message From [{response?.SenderId}]");
            OnStreamMessageReceived?.Invoke(response);
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(StreamMessage)}] => End Receiving Stream Message From [{response?.SenderId}]");
        }

        private void BuzzMessage(BuzzMessageResponse? response)
        {
            if (response is null)
                return;
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(OnBuzzMessageReceived)}] => Start Receiving Buzz Message From [{response.SenderId}]");
            OnBuzzMessageReceived?.Invoke(response);
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(OnBuzzMessageReceived)}] => End Receiving Buzz Message From [{response.SenderId}]");
        }

        private void CreateGroup(CreateGroupResponse? response)
        {
            if (response is null)
                return;
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(OnCreateGroupReceived)}] => Start Creating Group [{response.Group.Name}]");
            OnCreateGroupReceived?.Invoke(response);
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(OnCreateGroupReceived)}] => End Creating Group [{response.Group.Name}]");
        }

        private void JoinGroup(JoinGroupResponse? response)
        {
            if (response == null || response.Group == null || response.User == null)
                return;
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(OnJoinGroupReceived)}] => Start Joining [{response.User.Name}] To Group [{response.Group.Name}]");
            OnJoinGroupReceived?.Invoke(response);
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(OnJoinGroupReceived)}] => End Joining [{response.User.Name}] To Group [{response.Group.Name}]");
        }

        private void LeaveGroup(LeaveGroupResponse? response)
        {
            if (response == null || response.Group == null || response.User == null)
                return;
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(OnLeaveGroupReceived)}] => Start Leaving [{response.User.Name}] From Group [{response.Group.Name}]");
            OnLeaveGroupReceived?.Invoke(response);
            logger.Information($"[{nameof(SignalRConnectionManager)}].[{nameof(OnLeaveGroupReceived)}] => End Leaving [{response.User.Name}] From Group [{response.Group.Name}]");
        }
        #endregion
    }
}
