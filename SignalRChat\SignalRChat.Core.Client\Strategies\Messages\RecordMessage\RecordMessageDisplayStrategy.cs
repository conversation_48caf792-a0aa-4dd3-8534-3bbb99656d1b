﻿namespace SignalRChat.Core.Client.Strategies.Messages.RecordMessage
{
    public class RecordMessageDisplayStrategy : IMessageDisplayStrategy, IDisposable
    {
        private IRecordMessageView _view;
        private byte[] Record;
        private MemoryStream stream;
        private WaveFileReader waveFileReader;
        private WaveOutEvent waveOut;
        private Stopwatch Stopwatch;
        private PlaybackState PlaybackState => waveOut.PlaybackState;

        public RecordMessageDisplayStrategy(IRecordMessageView view)
        {
            _view = view;
            _view.Play += _view_Play;
        }

        private async void _view_Play(object? sender, EventArgs e)
        {
            switch (PlaybackState)
            {
                case PlaybackState.Stopped:
                case PlaybackState.Paused:
                    await PlayWavFromByteArray();
                    break;
                case PlaybackState.Playing:
                    await PauseWavFromByteArray();
                    break;
            }
        }

        public void DisplayMessage(IMessageContainer tlpMain, ChatMessageDto chatMessageDto)
        {
            if (chatMessageDto != null && chatMessageDto is RecordChatMessageDto recordChatMessageDto)
            {
                // Set the record
                Record = recordChatMessageDto.Record;
                // Initial Record Tools
                Initial();
                _view.Text = $"{waveFileReader.TotalTime.ToString(@"hh\:mm\:ss")}";
            }

            tlpMain.Add(_view, 0, 0);
        }

        public int CalculateHeight()
        {
            if (_view is null)
                return default;

            int recordMessageHeight = _view.Height + 10;
            return recordMessageHeight;
        }

        public void SetBackColor(Color color) => _view.BackColor = color;

        void Initial()
        {
            stream = new MemoryStream(Record);
            waveFileReader = new WaveFileReader(stream);
            waveOut = new WaveOutEvent();
            waveOut.Init(waveFileReader);
            stream.Position = 0;
            Stopwatch = new Stopwatch();
        }

        async Task PlayWavFromByteArray()
        {
            _view.IsRecorderRunning = true;
            if (PlaybackState == PlaybackState.Stopped)
            {
                stream.Position = 0;
                Stopwatch.Reset();
            }
            waveOut.Play();
            Stopwatch.Start();

            // Wait for the playback to finish
            while (PlaybackState == PlaybackState.Playing)
            {
                // Update progress label
                _view.Text = $"{Stopwatch.Elapsed.ToString(@"hh\:mm\:ss")} / {waveFileReader.TotalTime.ToString(@"hh\:mm\:ss")}";
                if (stream.CanRead && stream.CanSeek && stream.Position <= stream.Length)
                {
                    long currentPosition = stream.Position;
                    long streamLength = stream.Length;
                    // Use floating-point arithmetic for the division
                    double position = (double)currentPosition / streamLength;
                    _view.Progress = position;
                }

                await Task.Delay(100);
            }

            _view.IsRecorderRunning = false;
            Stopwatch.Stop();
        }

        async Task PauseWavFromByteArray()
        {
            if (PlaybackState == PlaybackState.Playing)
            {
                _view.IsRecorderRunning = false;
                waveOut.Pause();
                Stopwatch.Stop();
            }

            await Task.FromResult(Task.CompletedTask);
        }

        public void Dispose()
        {
            Dispose();
            stream.Dispose();
            waveFileReader.Dispose();
            waveOut.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
