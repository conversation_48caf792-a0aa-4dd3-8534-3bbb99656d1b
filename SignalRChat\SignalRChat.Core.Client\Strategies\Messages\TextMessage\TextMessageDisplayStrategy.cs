﻿namespace SignalRChat.Core.Client.Strategies.Messages.TextMessage
{
    public class TextMessageDisplayStrategy : IMessageDisplayStrategy
    {
        private ITextMessageView _view;

        public TextMessageDisplayStrategy(ITextMessageView view)
        {
            _view = view;
        }

        public void DisplayMessage(IMessageContainer messageContainer, ChatMessageDto chatMessageDto)
        {
            TextChatMessageDto? textChatMessageDto = chatMessageDto as TextChatMessageDto;
            if (textChatMessageDto != null)
            {
                _view.AutoSize = true;
                _view.Text = textChatMessageDto.Message;
            }

            messageContainer.Add(_view, 0, 0);
        }

        public int CalculateHeight()
        {
            if (_view is null)
                return default;

            int textMessageHeight = _view.Height;
            return textMessageHeight;
        }

        public void SetBackColor(Color color) => _view.BackColor = color;
    }
}
