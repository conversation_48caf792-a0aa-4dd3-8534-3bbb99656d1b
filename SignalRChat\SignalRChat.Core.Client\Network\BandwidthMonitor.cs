using SignalRChat.Core.Client.Configuration;
using System.Collections.Concurrent;

namespace SignalRChat.Core.Client.Network
{
    /// <summary>
    /// Network transmission data
    /// </summary>
    public class TransmissionData
    {
        public DateTime Timestamp { get; set; }
        public long BytesSent { get; set; }
        public TimeSpan TransmissionTime { get; set; }
        public bool Success { get; set; }
    }

    /// <summary>
    /// Bandwidth statistics
    /// </summary>
    public class BandwidthStatistics
    {
        public DateTime Timestamp { get; set; }
        public long CurrentBandwidthKbps { get; set; }
        public long AverageBandwidthKbps { get; set; }
        public long PeakBandwidthKbps { get; set; }
        public double PacketLossPercent { get; set; }
        public TimeSpan AverageLatency { get; set; }
        public int QueuedTransmissions { get; set; }
        public long TotalBytesSent { get; set; }
    }

    /// <summary>
    /// Monitors network bandwidth and provides adaptive optimization
    /// </summary>
    public class BandwidthMonitor : IDisposable
    {
        private readonly ConcurrentQueue<TransmissionData> _transmissionHistory;
        private readonly Timer _statisticsTimer;
        private readonly object _lockObject = new();
        
        private long _totalBytesSent;
        private long _totalTransmissions;
        private long _failedTransmissions;
        private DateTime _startTime;
        private BandwidthStatistics _currentStats;
        private bool _disposed;

        public event Action<BandwidthStatistics>? StatisticsUpdated;

        public BandwidthStatistics CurrentStatistics => _currentStats;
        public bool IsMonitoring { get; private set; }

        public BandwidthMonitor()
        {
            _transmissionHistory = new ConcurrentQueue<TransmissionData>();
            _currentStats = new BandwidthStatistics();
            _statisticsTimer = new Timer(UpdateStatistics, null, Timeout.Infinite, Timeout.Infinite);
            _startTime = DateTime.Now;
        }

        /// <summary>
        /// Starts bandwidth monitoring
        /// </summary>
        public void StartMonitoring(TimeSpan updateInterval)
        {
            if (_disposed) return;

            IsMonitoring = true;
            _startTime = DateTime.Now;
            _statisticsTimer.Change(TimeSpan.Zero, updateInterval);
        }

        /// <summary>
        /// Stops bandwidth monitoring
        /// </summary>
        public void StopMonitoring()
        {
            IsMonitoring = false;
            _statisticsTimer.Change(Timeout.Infinite, Timeout.Infinite);
        }

        /// <summary>
        /// Records a transmission event
        /// </summary>
        public void RecordTransmission(long bytesSent, TimeSpan transmissionTime, bool success = true)
        {
            if (_disposed || !IsMonitoring) return;

            var transmission = new TransmissionData
            {
                Timestamp = DateTime.Now,
                BytesSent = bytesSent,
                TransmissionTime = transmissionTime,
                Success = success
            };

            _transmissionHistory.Enqueue(transmission);

            lock (_lockObject)
            {
                _totalBytesSent += bytesSent;
                _totalTransmissions++;
                if (!success)
                    _failedTransmissions++;
            }

            // Keep only last 100 transmissions
            while (_transmissionHistory.Count > 100)
            {
                _transmissionHistory.TryDequeue(out _);
            }
        }

        /// <summary>
        /// Updates bandwidth statistics
        /// </summary>
        private void UpdateStatistics(object? state)
        {
            if (_disposed || !IsMonitoring) return;

            try
            {
                var now = DateTime.Now;
                var recentTransmissions = GetRecentTransmissions(TimeSpan.FromSeconds(10));
                
                var stats = new BandwidthStatistics
                {
                    Timestamp = now,
                    CurrentBandwidthKbps = CalculateCurrentBandwidth(recentTransmissions),
                    AverageBandwidthKbps = CalculateAverageBandwidth(),
                    PeakBandwidthKbps = CalculatePeakBandwidth(),
                    PacketLossPercent = CalculatePacketLoss(),
                    AverageLatency = CalculateAverageLatency(recentTransmissions),
                    QueuedTransmissions = 0, // Would be set by transmission queue
                    TotalBytesSent = _totalBytesSent
                };

                _currentStats = stats;
                StatisticsUpdated?.Invoke(stats);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating bandwidth statistics: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets recent transmissions within the specified time window
        /// </summary>
        private List<TransmissionData> GetRecentTransmissions(TimeSpan timeWindow)
        {
            var cutoff = DateTime.Now - timeWindow;
            return _transmissionHistory.Where(t => t.Timestamp >= cutoff).ToList();
        }

        /// <summary>
        /// Calculates current bandwidth based on recent transmissions
        /// </summary>
        private long CalculateCurrentBandwidth(List<TransmissionData> recentTransmissions)
        {
            if (!recentTransmissions.Any()) return 0;

            var totalBytes = recentTransmissions.Sum(t => t.BytesSent);
            var timeSpan = DateTime.Now - recentTransmissions.Min(t => t.Timestamp);
            
            if (timeSpan.TotalSeconds > 0)
            {
                var bytesPerSecond = totalBytes / timeSpan.TotalSeconds;
                return (long)(bytesPerSecond * 8 / 1024); // Convert to Kbps
            }

            return 0;
        }

        /// <summary>
        /// Calculates average bandwidth since monitoring started
        /// </summary>
        private long CalculateAverageBandwidth()
        {
            var elapsed = DateTime.Now - _startTime;
            if (elapsed.TotalSeconds > 0)
            {
                var bytesPerSecond = _totalBytesSent / elapsed.TotalSeconds;
                return (long)(bytesPerSecond * 8 / 1024);
            }
            return 0;
        }

        /// <summary>
        /// Calculates peak bandwidth from transmission history
        /// </summary>
        private long CalculatePeakBandwidth()
        {
            var transmissions = _transmissionHistory.ToArray();
            if (transmissions.Length < 2) return 0;

            long peak = 0;
            for (int i = 0; i < transmissions.Length - 1; i++)
            {
                var current = transmissions[i];
                var next = transmissions[i + 1];
                var timeDiff = (next.Timestamp - current.Timestamp).TotalSeconds;
                
                if (timeDiff > 0)
                {
                    var bandwidth = (long)(current.BytesSent * 8 / 1024 / timeDiff);
                    peak = Math.Max(peak, bandwidth);
                }
            }

            return peak;
        }

        /// <summary>
        /// Calculates packet loss percentage
        /// </summary>
        private double CalculatePacketLoss()
        {
            if (_totalTransmissions == 0) return 0;
            return (_failedTransmissions * 100.0) / _totalTransmissions;
        }

        /// <summary>
        /// Calculates average transmission latency
        /// </summary>
        private TimeSpan CalculateAverageLatency(List<TransmissionData> transmissions)
        {
            if (!transmissions.Any()) return TimeSpan.Zero;

            var averageMs = transmissions.Average(t => t.TransmissionTime.TotalMilliseconds);
            return TimeSpan.FromMilliseconds(averageMs);
        }

        /// <summary>
        /// Determines if bandwidth is sufficient for the given configuration
        /// </summary>
        public bool IsBandwidthSufficient(StreamingConfiguration config)
        {
            var requiredBandwidth = EstimateRequiredBandwidth(config);
            var availableBandwidth = _currentStats.CurrentBandwidthKbps;
            
            // Consider packet loss and add safety margin
            var effectiveBandwidth = availableBandwidth * (1 - _currentStats.PacketLossPercent / 100) * 0.8;
            
            return effectiveBandwidth >= requiredBandwidth;
        }

        /// <summary>
        /// Estimates required bandwidth for streaming configuration
        /// </summary>
        private long EstimateRequiredBandwidth(StreamingConfiguration config)
        {
            // Estimate based on resolution, frame rate, and quality
            var pixelCount = config.Width * config.Height;
            var bytesPerPixel = GetBytesPerPixelEstimate(config.Quality, config.CompressionType);
            var bytesPerFrame = pixelCount * bytesPerPixel;
            var bytesPerSecond = bytesPerFrame * config.TargetFrameRate;
            
            return (long)(bytesPerSecond * 8 / 1024); // Convert to Kbps
        }

        /// <summary>
        /// Gets estimated bytes per pixel based on quality and compression
        /// </summary>
        private double GetBytesPerPixelEstimate(VideoQuality quality, CompressionType compression)
        {
            var baseEstimate = quality switch
            {
                VideoQuality.Low => 0.05,
                VideoQuality.Medium => 0.1,
                VideoQuality.High => 0.2,
                VideoQuality.Ultra => 0.4,
                _ => 0.1
            };

            // Adjust for compression type
            return compression switch
            {
                CompressionType.None => baseEstimate * 4,
                CompressionType.Png => baseEstimate * 2,
                CompressionType.Jpeg => baseEstimate,
                CompressionType.WebP => baseEstimate * 0.8,
                CompressionType.Auto => baseEstimate,
                _ => baseEstimate
            };
        }

        /// <summary>
        /// Suggests optimal configuration based on current bandwidth
        /// </summary>
        public StreamingConfiguration SuggestOptimalConfiguration(StreamingConfiguration baseConfig)
        {
            var optimized = baseConfig.Clone();
            var availableBandwidth = _currentStats.CurrentBandwidthKbps;

            // Adjust quality based on available bandwidth
            if (availableBandwidth < 1000)
            {
                optimized.Quality = VideoQuality.Low;
                optimized.TargetFrameRate = Math.Min(optimized.TargetFrameRate, 10);
                optimized.Width = Math.Min(optimized.Width, 640);
                optimized.Height = Math.Min(optimized.Height, 480);
            }
            else if (availableBandwidth < 3000)
            {
                optimized.Quality = VideoQuality.Medium;
                optimized.TargetFrameRate = Math.Min(optimized.TargetFrameRate, 15);
                optimized.Width = Math.Min(optimized.Width, 1280);
                optimized.Height = Math.Min(optimized.Height, 720);
            }
            else if (availableBandwidth < 5000)
            {
                optimized.Quality = VideoQuality.High;
                optimized.TargetFrameRate = Math.Min(optimized.TargetFrameRate, 24);
            }

            // Adjust for packet loss
            if (_currentStats.PacketLossPercent > 5)
            {
                optimized.TargetFrameRate = (int)(optimized.TargetFrameRate * 0.8);
                optimized.JpegQuality = Math.Max(50, optimized.JpegQuality - 10);
            }

            optimized.Validate();
            return optimized;
        }

        /// <summary>
        /// Gets bandwidth utilization percentage
        /// </summary>
        public double GetBandwidthUtilization(StreamingConfiguration config)
        {
            var required = EstimateRequiredBandwidth(config);
            var available = Math.Max(1, _currentStats.CurrentBandwidthKbps);
            return Math.Min(100, (required * 100.0) / available);
        }

        public void Dispose()
        {
            if (_disposed) return;

            _disposed = true;
            StopMonitoring();
            _statisticsTimer?.Dispose();
        }
    }
}
