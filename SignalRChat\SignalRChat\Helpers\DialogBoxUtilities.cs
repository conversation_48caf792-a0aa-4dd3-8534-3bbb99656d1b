﻿namespace SignalRChat.Helpers
{
    public static class DialogBoxUtilities
    {
        public static string OpenFile(string caption, string filter = "All files (*.*)|*.*")
        {
            OpenFileDialog diag = new OpenFileDialog();
            diag.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.MyPictures);
            diag.Title = caption;
            diag.Filter = filter;
            diag.CheckFileExists = true;
            diag.CheckPathExists = true;
            diag.RestoreDirectory = true;

            if (diag.ShowDialog() == DialogResult.OK)
                return diag.FileName;
            return string.Empty;
        }
    }
}
