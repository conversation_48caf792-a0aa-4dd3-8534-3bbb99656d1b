namespace SignalRChat.Core.Client.Examples
{
    /// <summary>
    /// Example demonstrating how to use the enhanced streaming features
    /// </summary>
    public class EnhancedStreamingExample
    {
        private readonly EnhancedStreamMessageService _streamingService;
        private readonly StreamingConfiguration _config;
        private readonly PerformanceMonitor _performanceMonitor;
        private readonly BandwidthMonitor _bandwidthMonitor;
        private readonly StreamingMetricsCollector _metricsCollector;

        public EnhancedStreamingExample(IServiceProvider serviceProvider)
        {
            // Get services from DI container
            _streamingService = serviceProvider.GetRequiredService<EnhancedStreamMessageService>();
            _config = serviceProvider.GetRequiredService<StreamingConfiguration>();
            _performanceMonitor = serviceProvider.GetRequiredService<PerformanceMonitor>();
            _bandwidthMonitor = serviceProvider.GetRequiredService<BandwidthMonitor>();
            _metricsCollector = serviceProvider.GetRequiredService<StreamingMetricsCollector>();
        }

        /// <summary>
        /// Example 1: Basic enhanced streaming with default settings
        /// </summary>
        public async Task BasicEnhancedStreaming()
        {
            Console.WriteLine("Starting basic enhanced streaming...");

            // Start streaming with default configuration
            var success = await _streamingService.SendAsync();
            if (success)
            {
                Console.WriteLine("Streaming started successfully!");
                
                // Let it run for 30 seconds
                await Task.Delay(TimeSpan.FromSeconds(30));
                
                // Stop streaming
                await _streamingService.StopStreaming();
                Console.WriteLine("Streaming stopped.");
            }
        }

        /// <summary>
        /// Example 2: Custom configuration for high-quality streaming
        /// </summary>
        public async Task HighQualityStreaming()
        {
            Console.WriteLine("Starting high-quality streaming...");

            // Create custom high-quality configuration
            var highQualityConfig = new StreamingConfiguration
            {
                TargetFrameRate = 30,
                MinFrameRate = 15,
                MaxFrameRate = 60,
                Width = 1920,
                Height = 1080,
                Quality = VideoQuality.High,
                JpegQuality = 90,
                CompressionType = CompressionType.Jpeg,
                AdaptiveFrameRate = true,
                AdaptiveResolution = true,
                AdaptiveBandwidth = true,
                MaxCpuUsagePercent = 80,
                MaxMemoryUsageMB = 1024,
                MaxBandwidthKbps = 8000,
                PerformanceMode = PerformanceMode.Performance
            };

            // Update streaming configuration
            _streamingService.UpdateConfiguration(highQualityConfig);

            // Start streaming
            await _streamingService.SendAsync();
            
            // Monitor for 1 minute
            await MonitorStreamingPerformance(TimeSpan.FromMinutes(1));
            
            await _streamingService.StopStreaming();
        }

        /// <summary>
        /// Example 3: Power-saving streaming for mobile/battery scenarios
        /// </summary>
        public async Task PowerSavingStreaming()
        {
            Console.WriteLine("Starting power-saving streaming...");

            var powerSavingConfig = StreamingConfiguration.GetPreset(VideoQuality.Low);
            powerSavingConfig.TargetFrameRate = 10;
            powerSavingConfig.MaxCpuUsagePercent = 50;
            powerSavingConfig.MaxMemoryUsageMB = 256;
            powerSavingConfig.PerformanceMode = PerformanceMode.PowerSaver;
            powerSavingConfig.EnableMemoryPooling = true;

            _streamingService.UpdateConfiguration(powerSavingConfig);
            await _streamingService.SendAsync();
            
            // Monitor for 2 minutes
            await MonitorStreamingPerformance(TimeSpan.FromMinutes(2));
            
            await _streamingService.StopStreaming();
        }

        /// <summary>
        /// Example 4: Adaptive streaming with real-time monitoring
        /// </summary>
        public async Task AdaptiveStreamingWithMonitoring()
        {
            Console.WriteLine("Starting adaptive streaming with real-time monitoring...");

            // Subscribe to performance events
            _performanceMonitor.MetricsUpdated += OnPerformanceMetricsUpdated;
            _bandwidthMonitor.StatisticsUpdated += OnBandwidthStatsUpdated;
            _metricsCollector.MetricsUpdated += OnStreamingMetricsUpdated;

            // Start monitoring
            _performanceMonitor.StartMonitoring(TimeSpan.FromSeconds(1));
            _bandwidthMonitor.StartMonitoring(TimeSpan.FromSeconds(2));
            _metricsCollector.StartCollection(TimeSpan.FromSeconds(5));

            // Start streaming with adaptive configuration
            var adaptiveConfig = StreamingConfiguration.GetPreset(VideoQuality.Medium);
            adaptiveConfig.AdaptiveFrameRate = true;
            adaptiveConfig.AdaptiveResolution = true;
            adaptiveConfig.AdaptiveBandwidth = true;
            
            _streamingService.UpdateConfiguration(adaptiveConfig);
            await _streamingService.SendAsync();

            // Let adaptive streaming run for 5 minutes
            await Task.Delay(TimeSpan.FromMinutes(5));

            // Generate and display performance report
            var report = _metricsCollector.GeneratePerformanceReport();
            Console.WriteLine(report);

            // Stop everything
            await _streamingService.StopStreaming();
            _performanceMonitor.StopMonitoring();
            _bandwidthMonitor.StopMonitoring();
            _metricsCollector.StopCollection();
        }

        /// <summary>
        /// Example 5: Manual quality adjustment based on conditions
        /// </summary>
        public async Task ManualQualityAdjustment()
        {
            Console.WriteLine("Starting streaming with manual quality adjustment...");

            await _streamingService.SendAsync();

            // Simulate different network conditions and adjust accordingly
            for (int i = 0; i < 10; i++)
            {
                await Task.Delay(TimeSpan.FromSeconds(30));

                var currentStats = _streamingService.CurrentBandwidth;
                var currentPerf = _streamingService.CurrentPerformance;

                Console.WriteLine($"Iteration {i + 1}:");
                Console.WriteLine($"  Bandwidth: {currentStats.CurrentBandwidthKbps} Kbps");
                Console.WriteLine($"  CPU: {currentPerf.CpuUsagePercent:F1}%");
                Console.WriteLine($"  Memory: {currentPerf.MemoryUsageMB} MB");
                Console.WriteLine($"  Frame Rate: {_streamingService.CurrentFrameRate} FPS");

                // Adjust quality based on conditions
                var newConfig = _config.Clone();
                
                if (currentStats.CurrentBandwidthKbps < 2000)
                {
                    newConfig.Quality = VideoQuality.Low;
                    newConfig.TargetFrameRate = 10;
                    Console.WriteLine("  -> Switching to low quality due to low bandwidth");
                }
                else if (currentPerf.CpuUsagePercent > 80)
                {
                    newConfig.JpegQuality = Math.Max(50, newConfig.JpegQuality - 10);
                    newConfig.TargetFrameRate = Math.Max(10, newConfig.TargetFrameRate - 2);
                    Console.WriteLine("  -> Reducing quality due to high CPU usage");
                }
                else if (currentPerf.CpuUsagePercent < 40 && currentStats.CurrentBandwidthKbps > 5000)
                {
                    newConfig.Quality = VideoQuality.High;
                    newConfig.TargetFrameRate = Math.Min(30, newConfig.TargetFrameRate + 2);
                    Console.WriteLine("  -> Increasing quality due to good conditions");
                }

                _streamingService.UpdateConfiguration(newConfig);
            }

            await _streamingService.StopStreaming();
        }

        /// <summary>
        /// Monitors streaming performance and displays real-time statistics
        /// </summary>
        private async Task MonitorStreamingPerformance(TimeSpan duration)
        {
            var endTime = DateTime.Now + duration;
            
            while (DateTime.Now < endTime)
            {
                var stats = _streamingService.GetStatistics();
                
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] " +
                    $"FPS: {stats.FrameRate}, " +
                    $"CPU: {stats.Performance.CpuUsagePercent:F1}%, " +
                    $"Memory: {stats.Performance.MemoryUsageMB}MB, " +
                    $"Bandwidth: {stats.Bandwidth.CurrentBandwidthKbps}Kbps, " +
                    $"Frames: {stats.TotalFrames}");

                await Task.Delay(TimeSpan.FromSeconds(5));
            }
        }

        /// <summary>
        /// Event handler for performance metrics updates
        /// </summary>
        private void OnPerformanceMetricsUpdated(PerformanceMetrics metrics)
        {
            if (metrics.CpuUsagePercent > 90)
            {
                Console.WriteLine($"WARNING: High CPU usage detected: {metrics.CpuUsagePercent:F1}%");
            }

            if (metrics.MemoryUsageMB > 1000)
            {
                Console.WriteLine($"WARNING: High memory usage detected: {metrics.MemoryUsageMB} MB");
            }

            if (metrics.DroppedFrames > 0)
            {
                Console.WriteLine($"WARNING: Frames being dropped: {metrics.DroppedFrames}");
            }
        }

        /// <summary>
        /// Event handler for bandwidth statistics updates
        /// </summary>
        private void OnBandwidthStatsUpdated(BandwidthStatistics stats)
        {
            if (stats.PacketLossPercent > 5)
            {
                Console.WriteLine($"WARNING: High packet loss: {stats.PacketLossPercent:F1}%");
            }

            if (stats.AverageLatency.TotalMilliseconds > 200)
            {
                Console.WriteLine($"WARNING: High latency: {stats.AverageLatency.TotalMilliseconds:F0}ms");
            }
        }

        /// <summary>
        /// Event handler for streaming metrics updates
        /// </summary>
        private void OnStreamingMetricsUpdated(StreamingMetrics metrics)
        {
            if (metrics.OverallScore < 50)
            {
                Console.WriteLine($"WARNING: Poor streaming quality score: {metrics.OverallScore:F1}/100");
            }

            // Log significant adaptations
            if (metrics.FrameRateAdaptations > 0 || metrics.ResolutionAdaptations > 0)
            {
                Console.WriteLine($"INFO: Adaptations - FPS: {metrics.FrameRateAdaptations}, " +
                    $"Resolution: {metrics.ResolutionAdaptations}, Quality: {metrics.QualityAdaptations}");
            }
        }

        /// <summary>
        /// Example usage of all streaming features
        /// </summary>
        public static async Task RunAllExamples(IServiceProvider serviceProvider)
        {
            var example = new EnhancedStreamingExample(serviceProvider);

            Console.WriteLine("=== Enhanced Streaming Examples ===\n");

            try
            {
                // Run each example
                await example.BasicEnhancedStreaming();
                await Task.Delay(2000);

                await example.PowerSavingStreaming();
                await Task.Delay(2000);

                await example.HighQualityStreaming();
                await Task.Delay(2000);

                await example.ManualQualityAdjustment();
                await Task.Delay(2000);

                await example.AdaptiveStreamingWithMonitoring();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during streaming examples: {ex.Message}");
            }

            Console.WriteLine("\n=== All examples completed ===");
        }
    }
}
