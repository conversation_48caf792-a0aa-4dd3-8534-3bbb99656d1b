using SignalRChat.Core.Client.Configuration;
using System.Drawing.Imaging;

namespace SignalRChat.Core.Client.Helpers
{
    /// <summary>
    /// Enhanced image processing with compression, scaling, and optimization
    /// </summary>
    public static class EnhancedImageProcessor
    {
        private static readonly object _lockObject = new();
        private static readonly Dictionary<string, ImageCodecInfo> _encoders = new();

        static EnhancedImageProcessor()
        {
            InitializeEncoders();
        }

        /// <summary>
        /// Initializes image encoders
        /// </summary>
        private static void InitializeEncoders()
        {
            foreach (var codec in ImageCodecInfo.GetImageEncoders())
            {
                _encoders[codec.MimeType.ToLower()] = codec;
            }
        }

        /// <summary>
        /// Processes and compresses an image based on streaming configuration
        /// </summary>
        public static byte[] ProcessImage(Bitmap originalImage, StreamingConfiguration config)
        {
            if (originalImage == null)
                return Array.Empty<byte>();

            try
            {
                using var processedImage = ApplyImageProcessing(originalImage, config);
                return CompressImage(processedImage, config);
            }
            catch (Exception ex)
            {
                // Fallback to basic conversion
                return Converters.ImageToByteArray(originalImage);
            }
        }

        /// <summary>
        /// Applies image processing including scaling and optimization
        /// </summary>
        private static Bitmap ApplyImageProcessing(Bitmap originalImage, StreamingConfiguration config)
        {
            var targetWidth = (int)(config.Width * config.ScaleFactor);
            var targetHeight = (int)(config.Height * config.ScaleFactor);

            // If no scaling needed, return original
            if (originalImage.Width == targetWidth && originalImage.Height == targetHeight)
                return new Bitmap(originalImage);

            // Calculate aspect ratio preserving dimensions
            var aspectRatio = (double)originalImage.Width / originalImage.Height;
            var targetAspectRatio = (double)targetWidth / targetHeight;

            int finalWidth, finalHeight;
            if (aspectRatio > targetAspectRatio)
            {
                finalWidth = targetWidth;
                finalHeight = (int)(targetWidth / aspectRatio);
            }
            else
            {
                finalWidth = (int)(targetHeight * aspectRatio);
                finalHeight = targetHeight;
            }

            // Create scaled image with high quality
            var scaledImage = new Bitmap(finalWidth, finalHeight);
            using (var graphics = Graphics.FromImage(scaledImage))
            {
                graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                graphics.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;
                graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;

                graphics.DrawImage(originalImage, 0, 0, finalWidth, finalHeight);
            }

            return scaledImage;
        }

        /// <summary>
        /// Compresses image based on configuration
        /// </summary>
        private static byte[] CompressImage(Bitmap image, StreamingConfiguration config)
        {
            var compressionType = config.CompressionType == CompressionType.Auto 
                ? DetermineOptimalCompression(image, config) 
                : config.CompressionType;

            return compressionType switch
            {
                CompressionType.Jpeg => CompressAsJpeg(image, config.JpegQuality),
                CompressionType.Png => CompressAsPng(image),
                CompressionType.WebP => CompressAsWebP(image, config.JpegQuality),
                CompressionType.None => ConvertToUncompressed(image),
                _ => CompressAsJpeg(image, config.JpegQuality)
            };
        }

        /// <summary>
        /// Determines optimal compression type based on image characteristics
        /// </summary>
        private static CompressionType DetermineOptimalCompression(Bitmap image, StreamingConfiguration config)
        {
            // Analyze image to determine best compression
            var hasTransparency = HasTransparency(image);
            var colorCount = EstimateColorCount(image);

            if (hasTransparency)
                return CompressionType.Png;

            if (colorCount < 256 && config.Quality <= VideoQuality.Medium)
                return CompressionType.Png;

            return CompressionType.Jpeg;
        }

        /// <summary>
        /// Checks if image has transparency
        /// </summary>
        private static bool HasTransparency(Bitmap image)
        {
            if (image.PixelFormat == PixelFormat.Format32bppArgb ||
                image.PixelFormat == PixelFormat.Format32bppPArgb)
            {
                // Quick check for transparency in corners
                var corners = new[]
                {
                    image.GetPixel(0, 0),
                    image.GetPixel(image.Width - 1, 0),
                    image.GetPixel(0, image.Height - 1),
                    image.GetPixel(image.Width - 1, image.Height - 1)
                };

                return corners.Any(c => c.A < 255);
            }
            return false;
        }

        /// <summary>
        /// Estimates color count in image
        /// </summary>
        private static int EstimateColorCount(Bitmap image)
        {
            var colors = new HashSet<Color>();
            var sampleSize = Math.Min(100, Math.Min(image.Width, image.Height));
            var stepX = Math.Max(1, image.Width / sampleSize);
            var stepY = Math.Max(1, image.Height / sampleSize);

            for (int x = 0; x < image.Width; x += stepX)
            {
                for (int y = 0; y < image.Height; y += stepY)
                {
                    colors.Add(image.GetPixel(x, y));
                    if (colors.Count > 1000) // Early exit for complex images
                        return colors.Count;
                }
            }

            return colors.Count;
        }

        /// <summary>
        /// Compresses image as JPEG
        /// </summary>
        private static byte[] CompressAsJpeg(Bitmap image, int quality)
        {
            using var stream = new MemoryStream();
            
            if (_encoders.TryGetValue("image/jpeg", out var jpegEncoder))
            {
                var encoderParams = new EncoderParameters(1);
                encoderParams.Param[0] = new EncoderParameter(Encoder.Quality, quality);
                image.Save(stream, jpegEncoder, encoderParams);
            }
            else
            {
                image.Save(stream, ImageFormat.Jpeg);
            }

            return stream.ToArray();
        }

        /// <summary>
        /// Compresses image as PNG
        /// </summary>
        private static byte[] CompressAsPng(Bitmap image)
        {
            using var stream = new MemoryStream();
            image.Save(stream, ImageFormat.Png);
            return stream.ToArray();
        }

        /// <summary>
        /// Compresses image as WebP (placeholder - would need WebP library)
        /// </summary>
        private static byte[] CompressAsWebP(Bitmap image, int quality)
        {
            // WebP compression would require additional library like ImageSharp
            // For now, fallback to JPEG
            return CompressAsJpeg(image, quality);
        }

        /// <summary>
        /// Converts to uncompressed format
        /// </summary>
        private static byte[] ConvertToUncompressed(Bitmap image)
        {
            using var stream = new MemoryStream();
            image.Save(stream, ImageFormat.Bmp);
            return stream.ToArray();
        }

        /// <summary>
        /// Estimates compression ratio
        /// </summary>
        public static double EstimateCompressionRatio(Bitmap originalImage, byte[] compressedData)
        {
            var originalSize = originalImage.Width * originalImage.Height * 4; // Assume 32bpp
            return compressedData.Length > 0 ? (double)originalSize / compressedData.Length : 1.0;
        }

        /// <summary>
        /// Creates a memory-efficient bitmap copy
        /// </summary>
        public static Bitmap CreateEfficientCopy(Bitmap source)
        {
            var rect = new Rectangle(0, 0, source.Width, source.Height);
            var pixelFormat = source.PixelFormat;

            // Use most efficient pixel format for the content
            if (pixelFormat == PixelFormat.Format32bppArgb && !HasTransparency(source))
                pixelFormat = PixelFormat.Format24bppRgb;

            var copy = new Bitmap(source.Width, source.Height, pixelFormat);
            using (var graphics = Graphics.FromImage(copy))
            {
                graphics.DrawImage(source, rect, rect, GraphicsUnit.Pixel);
            }

            return copy;
        }

        /// <summary>
        /// Optimizes bitmap for streaming
        /// </summary>
        public static Bitmap OptimizeForStreaming(Bitmap source, StreamingConfiguration config)
        {
            // Apply optimizations based on performance mode
            switch (config.PerformanceMode)
            {
                case PerformanceMode.PowerSaver:
                    return CreateLowQualityCopy(source);
                case PerformanceMode.Balanced:
                    return CreateEfficientCopy(source);
                case PerformanceMode.Performance:
                case PerformanceMode.MaxPerformance:
                    return new Bitmap(source);
                default:
                    return CreateEfficientCopy(source);
            }
        }

        /// <summary>
        /// Creates a low quality copy for power saving
        /// </summary>
        private static Bitmap CreateLowQualityCopy(Bitmap source)
        {
            var copy = new Bitmap(source.Width, source.Height, PixelFormat.Format16bppRgb565);
            using (var graphics = Graphics.FromImage(copy))
            {
                graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.Low;
                graphics.DrawImage(source, 0, 0, source.Width, source.Height);
            }
            return copy;
        }
    }
}
