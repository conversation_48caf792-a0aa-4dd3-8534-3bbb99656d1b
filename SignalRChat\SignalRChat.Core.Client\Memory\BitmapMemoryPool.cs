using System.Collections.Concurrent;
using System.Drawing.Imaging;

namespace SignalRChat.Core.Client.Memory
{
    /// <summary>
    /// Memory pool for efficient bitmap management
    /// </summary>
    public class BitmapMemoryPool : IDisposable
    {
        private readonly ConcurrentQueue<PooledBitmap> _availableBitmaps;
        private readonly ConcurrentDictionary<int, PooledBitmap> _rentedBitmaps;
        private readonly object _lockObject = new();
        private readonly int _maxPoolSize;
        private readonly int _defaultWidth;
        private readonly int _defaultHeight;
        private readonly PixelFormat _pixelFormat;
        
        private int _currentPoolSize;
        private bool _disposed;

        public int AvailableCount => _availableBitmaps.Count;
        public int RentedCount => _rentedBitmaps.Count;
        public int TotalCreated { get; private set; }

        public BitmapMemoryPool(int maxPoolSize = 10, int defaultWidth = 1920, 
                               int defaultHeight = 1080, PixelFormat pixelFormat = PixelFormat.Format24bppRgb)
        {
            _maxPoolSize = maxPoolSize;
            _defaultWidth = defaultWidth;
            _defaultHeight = defaultHeight;
            _pixelFormat = pixelFormat;
            
            _availableBitmaps = new ConcurrentQueue<PooledBitmap>();
            _rentedBitmaps = new ConcurrentDictionary<int, PooledBitmap>();
        }

        /// <summary>
        /// Rents a bitmap from the pool
        /// </summary>
        public PooledBitmap Rent(int width = 0, int height = 0, PixelFormat? pixelFormat = null)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(BitmapMemoryPool));

            width = width > 0 ? width : _defaultWidth;
            height = height > 0 ? height : _defaultHeight;
            pixelFormat ??= _pixelFormat;

            // Try to get from pool first
            while (_availableBitmaps.TryDequeue(out var pooledBitmap))
            {
                if (pooledBitmap.Bitmap.Width == width && 
                    pooledBitmap.Bitmap.Height == height && 
                    pooledBitmap.Bitmap.PixelFormat == pixelFormat)
                {
                    pooledBitmap.Reset();
                    _rentedBitmaps[pooledBitmap.Id] = pooledBitmap;
                    return pooledBitmap;
                }
                else
                {
                    // Dispose incompatible bitmap
                    pooledBitmap.Dispose();
                    Interlocked.Decrement(ref _currentPoolSize);
                }
            }

            // Create new bitmap if pool is empty or no suitable bitmap found
            var newBitmap = new Bitmap(width, height, pixelFormat.Value);
            var newPooledBitmap = new PooledBitmap(newBitmap, this);
            
            _rentedBitmaps[newPooledBitmap.Id] = newPooledBitmap;
            Interlocked.Increment(ref TotalCreated);
            
            return newPooledBitmap;
        }

        /// <summary>
        /// Returns a bitmap to the pool
        /// </summary>
        internal void Return(PooledBitmap pooledBitmap)
        {
            if (_disposed || pooledBitmap.IsDisposed)
                return;

            _rentedBitmaps.TryRemove(pooledBitmap.Id, out _);

            lock (_lockObject)
            {
                if (_currentPoolSize < _maxPoolSize && !_disposed)
                {
                    _availableBitmaps.Enqueue(pooledBitmap);
                    Interlocked.Increment(ref _currentPoolSize);
                }
                else
                {
                    pooledBitmap.Dispose();
                }
            }
        }

        /// <summary>
        /// Clears the pool and disposes all bitmaps
        /// </summary>
        public void Clear()
        {
            lock (_lockObject)
            {
                while (_availableBitmaps.TryDequeue(out var bitmap))
                {
                    bitmap.Dispose();
                }
                _currentPoolSize = 0;
            }
        }

        /// <summary>
        /// Gets pool statistics
        /// </summary>
        public (int Available, int Rented, int Total, int MaxSize) GetStatistics()
        {
            return (AvailableCount, RentedCount, TotalCreated, _maxPoolSize);
        }

        public void Dispose()
        {
            if (_disposed) return;

            _disposed = true;
            
            // Dispose all rented bitmaps
            foreach (var rentedBitmap in _rentedBitmaps.Values)
            {
                rentedBitmap.Dispose();
            }
            _rentedBitmaps.Clear();

            // Clear the pool
            Clear();
        }
    }

    /// <summary>
    /// Pooled bitmap wrapper that automatically returns to pool when disposed
    /// </summary>
    public class PooledBitmap : IDisposable
    {
        private static int _nextId = 0;
        private readonly BitmapMemoryPool _pool;
        
        public int Id { get; }
        public Bitmap Bitmap { get; private set; }
        public bool IsDisposed { get; private set; }
        public DateTime CreatedAt { get; }
        public DateTime LastUsed { get; private set; }

        internal PooledBitmap(Bitmap bitmap, BitmapMemoryPool pool)
        {
            Id = Interlocked.Increment(ref _nextId);
            Bitmap = bitmap ?? throw new ArgumentNullException(nameof(bitmap));
            _pool = pool ?? throw new ArgumentNullException(nameof(pool));
            CreatedAt = DateTime.Now;
            LastUsed = DateTime.Now;
        }

        /// <summary>
        /// Resets the bitmap for reuse
        /// </summary>
        internal void Reset()
        {
            LastUsed = DateTime.Now;
            
            // Clear the bitmap
            using (var graphics = Graphics.FromImage(Bitmap))
            {
                graphics.Clear(Color.Transparent);
            }
        }

        /// <summary>
        /// Gets graphics object for drawing on the bitmap
        /// </summary>
        public Graphics GetGraphics()
        {
            if (IsDisposed)
                throw new ObjectDisposedException(nameof(PooledBitmap));
            
            LastUsed = DateTime.Now;
            return Graphics.FromImage(Bitmap);
        }

        /// <summary>
        /// Copies another bitmap to this bitmap
        /// </summary>
        public void CopyFrom(Bitmap source)
        {
            if (IsDisposed)
                throw new ObjectDisposedException(nameof(PooledBitmap));

            using (var graphics = GetGraphics())
            {
                graphics.DrawImage(source, 0, 0, Bitmap.Width, Bitmap.Height);
            }
        }

        /// <summary>
        /// Creates a copy of this bitmap
        /// </summary>
        public Bitmap CreateCopy()
        {
            if (IsDisposed)
                throw new ObjectDisposedException(nameof(PooledBitmap));

            return new Bitmap(Bitmap);
        }

        public void Dispose()
        {
            if (IsDisposed) return;

            IsDisposed = true;
            
            if (_pool != null && !_pool._disposed)
            {
                _pool.Return(this);
            }
            else
            {
                Bitmap?.Dispose();
                Bitmap = null!;
            }
        }

        // Implicit conversion to Bitmap for convenience
        public static implicit operator Bitmap(PooledBitmap pooledBitmap)
        {
            return pooledBitmap.Bitmap;
        }
    }

    /// <summary>
    /// Memory pool manager for byte arrays
    /// </summary>
    public class ByteArrayPool : IDisposable
    {
        private readonly ConcurrentQueue<byte[]> _pool;
        private readonly int _maxPoolSize;
        private readonly int _arraySize;
        private int _currentPoolSize;
        private bool _disposed;

        public ByteArrayPool(int arraySize, int maxPoolSize = 20)
        {
            _arraySize = arraySize;
            _maxPoolSize = maxPoolSize;
            _pool = new ConcurrentQueue<byte[]>();
        }

        public byte[] Rent()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(ByteArrayPool));

            if (_pool.TryDequeue(out var array))
            {
                Interlocked.Decrement(ref _currentPoolSize);
                return array;
            }

            return new byte[_arraySize];
        }

        public void Return(byte[] array)
        {
            if (_disposed || array == null || array.Length != _arraySize)
                return;

            if (_currentPoolSize < _maxPoolSize)
            {
                Array.Clear(array, 0, array.Length);
                _pool.Enqueue(array);
                Interlocked.Increment(ref _currentPoolSize);
            }
        }

        public void Dispose()
        {
            if (_disposed) return;
            
            _disposed = true;
            while (_pool.TryDequeue(out _)) { }
        }
    }
}
