﻿namespace SignalRChat.CustomControls
{
    public partial class Chat : User<PERSON><PERSON><PERSON>, IDisposable
    {
        private readonly ChatCard _chatCard;
        private readonly ISession _session;
        private readonly ITextMessageService _textMessageService;
        private readonly IImageMessageService _imageMessageService;
        private readonly IRecordMessageService _recordMessageService;
        private readonly IStreamMessageService _streamMessageService;
        private readonly IParticipantTypingService _participantTypingService;
        private readonly IBuzzMessageService _buzzMessageService;
        private readonly ILeaveGroupService _leaveGroupService;
        private readonly System.Windows.Forms.Timer _timer = new System.Windows.Forms.Timer();
        private readonly Stopwatch _stopwatch = new Stopwatch();

        public Chat(ChatCard chatCard, IServiceProvider serviceProvider)
        {
            _chatCard = chatCard ?? throw new ArgumentNullException(nameof(chatCard));
            _session = serviceProvider.GetRequiredService<ISession>();
            _textMessageService = serviceProvider.GetRequiredService<ITextMessageService>();
            _imageMessageService = serviceProvider.GetRequiredService<IImageMessageService>();
            _recordMessageService = serviceProvider.GetRequiredService<IRecordMessageService>();
            _streamMessageService = serviceProvider.GetRequiredService<IStreamMessageService>();
            _participantTypingService = serviceProvider.GetRequiredService<IParticipantTypingService>();
            _buzzMessageService = serviceProvider.GetRequiredService<IBuzzMessageService>();
            _leaveGroupService = serviceProvider.GetRequiredService<ILeaveGroupService>();

            InitializeComponent();

            lblChatName.Text = _chatCard.Name;

            switch (_chatCard.ChatType)
            {
                case ChatType.IndividualChat:
                    btnOptions.Menu = individualChatMenuStrip;
                    break;
                case ChatType.GroupChat:
                    btnOptions.Menu = groupChatMenuStrip;
                    break;
            }

            btnOptions.ShowMenuUnderCursor = true;

            var messageList = new MessageList(_chatCard.ChatMessages, serviceProvider);
            tlpMain.Controls.Add(messageList, 0, 1);
            tlpMain.SetColumnSpan(messageList, tlpMain.ColumnCount);

            txtMessage.TextChanged += async (s, e) => await SendParticipantTyping();
            txtMessage.KeyUp += async (s, e) =>
            {
                if (e.KeyData == Keys.Enter)
                    await SendMessage();
            };
            btnSendMessage.Click += async (s, e) => await SendMessage();
            btnSendImage.Click += async (s, e) => await SendImage();
            btnSendRecord.Click += async (s, e) => await SendRecord();
            btnSendStream.Click += async (s, e) => await SendStream();
            btnSendBuzz.Click += (s, e) => _buzzMessageService.SendAsync(_session.SelectedChatCard.ClientId);
            if (_chatCard.ChatType == ChatType.GroupChat)
                tsmiLeaveGroup.Click += async (s, e) => await _leaveGroupService.SendAsync(_chatCard.ClientId);

            _timer.Tick += Timer_Tick;
        }

        private void Timer_Tick(object? sender, EventArgs e)
        {
            if (_recordMessageService.IsRecoding)
                txtMessage.Text = $"Recording .... {_stopwatch.Elapsed:hh\\:mm\\:ss}";
            else if (_streamMessageService.IsStreaming)
                txtMessage.Text = $"Streaming .... {_stopwatch.Elapsed:hh\\:mm\\:ss}";
        }

        private async Task SendMessage()
        {
            if (await _textMessageService.SendAsync(txtMessage.Text))
                txtMessage.Text = string.Empty;
        }

        private async Task SendImage()
        {
            var path = DialogBoxUtilities.OpenFile("Select image file", "Images (*.jpg;*.png)|*.jpg;*.png");
            if (string.IsNullOrEmpty(path) || !File.Exists(path))
                return;

            var image = await File.ReadAllBytesAsync(path);
            if (image != null && image.Length > 0)
                await _imageMessageService.SendAsync(image);
        }

        private async Task SendRecord()
        {
            if (!_recordMessageService.IsRecoding)
                await _recordMessageService.SendAsync();
            else
                await _recordMessageService.StopRecording();
            SetIsRecoding(_recordMessageService.IsRecoding);
        }

        private async Task SendStream()
        {
            if (!_streamMessageService.IsStreaming)
            {
                SetIsStreaming(true);
                await _streamMessageService.SendAsync();
            }
            else
            {
                SetIsStreaming(false);
                await _streamMessageService.StopStreaming();
            }
        }

        private async Task SendParticipantTyping()
        {
            if (string.IsNullOrEmpty(txtMessage.Text) || _streamMessageService.IsStreaming || _recordMessageService.IsRecoding)
                return;
            await _participantTypingService.SendAsync(_session.SelectedChatCard.ClientId);
        }

        private void SetIsRecoding(bool value)
        {
            this.SafelyInvokeAction(() =>
            {
                txtMessage.Enabled = !value;
                btnSendBuzz.Enabled = !value;
                btnSendImage.Enabled = !value;
                btnSendMessage.Enabled = !value;
                btnSendStream.Enabled = !value;

                if (value)
                {
                    _timer.Start();
                    _stopwatch.Restart();
                    _stopwatch.Start();
                }
                else
                {
                    _timer.Stop();
                    _stopwatch.Stop();
                    txtMessage.Text = string.Empty;
                }
            });
        }

        private void SetIsStreaming(bool value)
        {
            this.SafelyInvokeAction(() =>
            {
                txtMessage.Enabled = !value;
                btnSendBuzz.Enabled = !value;
                btnSendImage.Enabled = !value;
                btnSendMessage.Enabled = !value;
                btnSendRecord.Enabled = !value;

                if (value)
                {
                    _timer.Start();
                    _stopwatch.Restart();
                    _stopwatch.Start();
                }
                else
                {
                    _timer.Stop();
                    _stopwatch.Stop();
                    txtMessage.Text = string.Empty;
                }
            });
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Unsubscribe from events
                txtMessage.TextChanged -= async (s, e) => await _participantTypingService.SendAsync(_session.SelectedChatCard.ClientId);
                txtMessage.KeyUp -= async (s, e) =>
                {
                    if (e.KeyData == Keys.Enter)
                        await SendMessage();
                };
                btnSendMessage.Click -= async (s, e) => await SendMessage();
                btnSendImage.Click -= async (s, e) => await SendImage();
                btnSendRecord.Click -= async (s, e) => await SendRecord();
                btnSendStream.Click -= async (s, e) => await SendStream();
                btnSendBuzz.Click -= (s, e) => _buzzMessageService.SendAsync(_session.SelectedChatCard.ClientId);
                if (_chatCard.ChatType == ChatType.GroupChat)
                    tsmiLeaveGroup.Click -= async (s, e) => await _leaveGroupService.SendAsync(_chatCard.ClientId);

                _timer.Tick -= Timer_Tick;

                // Dispose managed resources
                _timer.Dispose();
                _stopwatch.Stop();
            }

            if (disposing && (components != null))
            {
                components.Dispose();
            }

            base.Dispose(disposing);
        }
    }
}
