﻿namespace SignalRChat.Views
{
    partial class ChatView
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            tlpMain = new TableLayoutPanel();
            contactCardList = new ContactCardList( _serviceProvider);
            tlpGroup = new TableLayoutPanel();
            btnCreateGroup = new Button();
            btnJoinGroup = new Button();
            tlpHeader = new TableLayoutPanel();
            lblHeaderName = new Label();
            lblHeaderState = new Label();
            tlpMain.SuspendLayout();
            tlpGroup.SuspendLayout();
            tlpHeader.SuspendLayout();
            SuspendLayout();
            // 
            // tlpMain
            // 
            tlpMain.ColumnCount = 2;
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));
            tlpMain.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F));
            tlpMain.Controls.Add(contactCardList, 0, 1);
            tlpMain.Controls.Add(tlpGroup, 0, 2);
            tlpMain.Controls.Add(tlpHeader, 0, 0);
            tlpMain.Dock = DockStyle.Fill;
            tlpMain.Location = new Point(0, 0);
            tlpMain.Name = "tlpMain";
            tlpMain.RowCount = 3;
            tlpMain.RowStyles.Add(new RowStyle(SizeType.Absolute, 33F));
            tlpMain.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tlpMain.RowStyles.Add(new RowStyle(SizeType.Absolute, 40F));
            tlpMain.Size = new Size(764, 435);
            tlpMain.TabIndex = 0;
            // 
            // contactCardList
            // 
            contactCardList.AutoScroll = true;
            contactCardList.AutoScrollMinSize = new Size(30, 0);
            contactCardList.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20F));
            contactCardList.Dock = DockStyle.Fill;
            contactCardList.Location = new Point(3, 3);
            contactCardList.Name = "contactCardList";
            contactCardList.Size = new Size(223, 389);
            contactCardList.TabIndex = 1;
            // 
            // tlpGroup
            // 
            tlpGroup.ColumnCount = 2;
            tlpGroup.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            tlpGroup.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            tlpGroup.Controls.Add(btnCreateGroup, 0, 0);
            tlpGroup.Controls.Add(btnJoinGroup, 1, 0);
            tlpGroup.Dock = DockStyle.Fill;
            tlpGroup.Location = new Point(3, 398);
            tlpGroup.Name = "tlpGroup";
            tlpGroup.RowCount = 1;
            tlpGroup.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
            tlpGroup.Size = new Size(223, 34);
            tlpGroup.TabIndex = 2;
            // 
            // btnCreateGroup
            // 
            btnCreateGroup.Dock = DockStyle.Fill;
            btnCreateGroup.Location = new Point(3, 3);
            btnCreateGroup.Name = "btnCreateGroup";
            btnCreateGroup.Size = new Size(105, 28);
            btnCreateGroup.TabIndex = 0;
            btnCreateGroup.Text = "Create Group";
            btnCreateGroup.UseVisualStyleBackColor = true;
            // 
            // btnJoinGroup
            // 
            btnJoinGroup.Dock = DockStyle.Fill;
            btnJoinGroup.Location = new Point(114, 3);
            btnJoinGroup.Name = "btnJoinGroup";
            btnJoinGroup.Size = new Size(106, 28);
            btnJoinGroup.TabIndex = 0;
            btnJoinGroup.Text = "Join Group";
            btnJoinGroup.UseVisualStyleBackColor = true;
            // 
            // tlpHeader
            // 
            tlpHeader.ColumnCount = 2;
            tlpMain.SetColumnSpan(tlpHeader, 2);
            tlpHeader.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            tlpHeader.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            tlpHeader.Controls.Add(lblHeaderName, 0, 0);
            tlpHeader.Controls.Add(lblHeaderState, 1, 0);
            tlpHeader.Dock = DockStyle.Fill;
            tlpHeader.Location = new Point(3, 3);
            tlpHeader.Name = "tlpHeader";
            tlpHeader.RowCount = 1;
            tlpHeader.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
            tlpHeader.Size = new Size(758, 27);
            tlpHeader.TabIndex = 3;
            // 
            // lblHeaderName
            // 
            lblHeaderName.Anchor = AnchorStyles.Left;
            lblHeaderName.AutoSize = true;
            lblHeaderName.Font = new Font("Segoe UI", 9.75F, FontStyle.Bold);
            lblHeaderName.Location = new Point(3, 5);
            lblHeaderName.Name = "lblHeaderName";
            lblHeaderName.Size = new Size(45, 17);
            lblHeaderName.TabIndex = 0;
            lblHeaderName.Text = "label1";
            // 
            // lblHeaderState
            // 
            lblHeaderState.Anchor = AnchorStyles.Right;
            lblHeaderState.AutoSize = true;
            lblHeaderState.Font = new Font("Segoe UI", 9.75F, FontStyle.Bold);
            lblHeaderState.Location = new Point(710, 5);
            lblHeaderState.Name = "lblHeaderState";
            lblHeaderState.Size = new Size(45, 17);
            lblHeaderState.TabIndex = 1;
            lblHeaderState.Text = "label1";
            // 
            // ChatView
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(764, 435);
            Controls.Add(tlpMain);
            Name = "ChatView";
            Text = "ChatView";
            tlpMain.ResumeLayout(false);
            tlpGroup.ResumeLayout(false);
            tlpHeader.ResumeLayout(false);
            tlpHeader.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private TableLayoutPanel tlpMain;
        private ContactCardList contactCardList;
        private TableLayoutPanel tlpGroup;
        private Button btnCreateGroup;
        private Button btnJoinGroup;
        private TableLayoutPanel tlpHeader;
        private Label lblHeaderName;
        private Label lblHeaderState;
    }
}