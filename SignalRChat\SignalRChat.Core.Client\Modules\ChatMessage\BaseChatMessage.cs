﻿namespace SignalRChat.Core.Client.Modules.ChatMessage
{
    public class BaseChatMessage(
        ISignalRConnectionManager signalRConnectionManager,
        ISendChatMessageManager sendChatMessageManager,
        IReceiveChatMessageManager receiveChatMessageManager,
        ISession session,
        ILogger logger)
    {
        protected readonly ISignalRConnectionManager _signalRConnectionManager = signalRConnectionManager;
        protected readonly ISendChatMessageManager _sendChatMessageManager = sendChatMessageManager;
        protected readonly IReceiveChatMessageManager _receiveChatMessageManager = receiveChatMessageManager;
        protected readonly ISession _session = session;
        protected readonly ILogger _logger = logger;

        protected bool CanSend(IChatMessageRequest? request)
        {
            return
                request != null
                && request.IsValid()
                && _signalRConnectionManager.IsConnected
                && _signalRConnectionManager.IsLoggedIn
                && _session.SelectedChatCard != null
                && _session.SelectedChatCard.IsLoggedIn;
        }

        protected async Task<bool> SendAsync(IChatMessageRequest request)
        {
            if (request is null)
                return false;

            return await _sendChatMessageManager.SendAsync(request);
        }

        protected async Task<bool> ReceiveAsync(IChatMessageResponse? response)
        {
            if (response is null)
                return false;

            return await _receiveChatMessageManager.ReceiveAsync(response);
        }
    }
}
