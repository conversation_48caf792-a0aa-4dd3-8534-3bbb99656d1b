﻿namespace SignalRChat.Core.Client.Modules.Group.JoinGroup
{
    public delegate void JoinGroupRequestedEventHandler(JoinGroupRequest? request);
    public delegate void JoinGroupReceivedEventHandler(JoinGroupResponse? response);

    public record JoinGroupRequest(Ulid GroupId);

    public record JoinGroupResponse(GroupDto? Group, UserDto? User);

    public interface IJoinGroupService : IBaseModuleService<JoinGroupRequest, JoinGroupResponse>
    {
        public event JoinGroupReceivedEventHandler OnGroupJoined;
        Task<bool> SendAsync(Ulid groupId);
    }

    public class JoinGroupService : IJoinGroupService
    {
        private readonly ISignalRConnectionManager _signalRConnectionManager;
        private readonly ISession _session;
        private readonly ILogger _logger;

        public event JoinGroupReceivedEventHandler OnGroupJoined;

        public JoinGroupService(ISignalRConnectionManager signalRConnectionManager, ISession session, ILogger logger)
        {
            _signalRConnectionManager = signalRConnectionManager;
            _session = session;
            _logger = logger;

            _signalRConnectionManager.OnJoinGroupReceived += Receive;
        }

        public async Task<bool> SendAsync(Ulid groupId)
        {
            if (!groupId.IsValid())
                return false;

            try
            {
                _logger.Information($"[{nameof(JoinGroupService)}].[{nameof(JoinGroup)}] => Start Joining Group [{groupId}]");
                var request = new JoinGroupRequest(groupId);
                bool result = await SendAsync(request);
                _logger.Information($"[{nameof(JoinGroupService)}].[{nameof(JoinGroup)}] => End Joining Group [{groupId}]");
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"[{nameof(JoinGroupService)}].[{nameof(JoinGroup)}] => Joining Group");
                return false;
            }
        }

        public bool CanSend(JoinGroupRequest? request)
        {
            return
                request != null
                && request.GroupId.IsValid()
                && _signalRConnectionManager.IsConnected
                && _signalRConnectionManager.IsLoggedIn;
        }

        public async Task<bool> SendAsync(JoinGroupRequest request)
        {
            try
            {
                _logger.Information($"[{nameof(JoinGroupService)}].[{nameof(SendAsync)}] => {nameof(request.GroupId)}: {request.GroupId}");
                await _signalRConnectionManager.InvokeCoreAsync("JoinGroup", [request]);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"[{nameof(JoinGroupService)}].[{nameof(SendAsync)}] => Failed Joining Group");
                return false;
            }
        }

        public void Receive(JoinGroupResponse? response)
        {
            if (response == null || response.Group == null || response.User == null)
                return;

            _logger.Information($"[{nameof(JoinGroupService)}].[{nameof(JoinGroup)}] => Start Joining [{response.User.Name}] To Group [{response.Group.Name}]");
            OnGroupJoined?.Invoke(response);

            ChatCard? groupCard = _session.GetChatCard(response.Group.ClientId);
            if (_signalRConnectionManager.IsLoggedIn)
            {
                ChatCard? userCard = _session.GetChatCard(response.User.ClientId);
                if (userCard != null)
                {
                    if (groupCard != null)
                    {
                        groupCard.GroupChatCard.Add(userCard);
                    }
                    else
                    {
                        groupCard = _session.ChatCards.TryAdd(new ChatCard(response.Group));
                        groupCard?.GroupChatCard.Add(userCard);
                    }
                }
                else
                {
                    if (groupCard != null)
                    {
                        groupCard.GroupChatCard.Add(new ChatCard(response.User));
                    }
                    else
                    {
                        groupCard = _session.ChatCards.TryAdd(new ChatCard(response.Group));
                        groupCard?.GroupChatCard.Add(new ChatCard(response.User));
                    }
                }
            }
            _logger.Information($"[{nameof(JoinGroupService)}].[{nameof(JoinGroup)}] => End Joining [{response.User.Name}] To Group [{response.Group.Name}]");
        }
    }
}
