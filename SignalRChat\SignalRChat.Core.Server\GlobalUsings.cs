﻿global using Microsoft.AspNetCore.Builder;
global using Microsoft.AspNetCore.SignalR;
global using Microsoft.EntityFrameworkCore;
global using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
global using Microsoft.Extensions.DependencyInjection;
global using Serilog;
global using SignalRChat.Core.Enums;
global using SignalRChat.Core.Helpers;
global using SignalRChat.Core.Models;
global using SignalRChat.Core.Modules.Authentication.Login;
global using SignalRChat.Core.Modules.Authentication.Logout;
global using SignalRChat.Core.Modules.ChatMessage;
global using SignalRChat.Core.Modules.ChatMessage.BuzzMessage;
global using SignalRChat.Core.Modules.ChatMessage.ImageMessage;
global using SignalRChat.Core.Modules.ChatMessage.ParticipantTyping;
global using SignalRChat.Core.Modules.ChatMessage.RecordMessage;
global using SignalRChat.Core.Modules.ChatMessage.StreamMessage;
global using SignalRChat.Core.Modules.ChatMessage.TextMessage;
global using SignalRChat.Core.Modules.Group.CreateGroup;
global using SignalRChat.Core.Modules.Group.JoinGroup;
global using SignalRChat.Core.Modules.Group.LeaveGroup;
global using SignalRChat.Core.Server.Data;
global using SignalRChat.Core.Server.Data.Repositories;
global using SignalRChat.Core.ViewModels;
global using System.Text.Json.Serialization;
