﻿namespace SignalRChat.CustomControls
{
    public partial class Message : UserControl, IDisposable
    {
        public readonly ChatMessageDto ChatMessageDto;
        private readonly IServiceProvider _serviceProvider;
        private readonly ISession _session;
        private readonly IMessageDisplayStrategy messageDisplayStrategy;

        private Label lblTime;
        private MessageContainer tlpMain;
        private const int PaddingValue = 5;
        private const int BorderRadius = 20;
        private const int BorderWidth = 1;

        public Message(ChatMessageDto chatMessageDto, IServiceProvider serviceProvider)
        {
            ChatMessageDto = chatMessageDto ?? throw new ArgumentNullException(nameof(chatMessageDto));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _session = _serviceProvider.GetRequiredService<ISession>();

            InitializeComponent();
            Dock = DockStyle.Fill;
            BackColor = MessageColors.BackColor;
            Padding = new Padding(PaddingValue);

            messageDisplayStrategy = MessageFactory.CreateMessage(ChatMessageDto.MessageType);

            InitializeUI();
            AdjustHeight();
        }

        public void UpdateDisplayMessage(ChatMessageDto chatMessageDto)
        {
            ArgumentNullException.ThrowIfNull(chatMessageDto);
            this.SafelyInvokeAction(() => messageDisplayStrategy.DisplayMessage(tlpMain, chatMessageDto));
        }

        private void InitializeUI()
        {
            lblTime = new Label();
            tlpMain = new MessageContainer();
            messageDisplayStrategy.DisplayMessage(tlpMain, ChatMessageDto);

            lblTime.AutoSize = true;
            lblTime.TextAlign = ContentAlignment.MiddleRight;
            lblTime.Dock = DockStyle.Bottom;
            lblTime.Text = ChatMessageDto.DateTime.ToShortTimeString();

            tlpMain.Controls.Add(lblTime, 1, 1);

            Controls.Add(tlpMain);
        }

        private void SetBackColor(Color color)
        {
            ArgumentNullException.ThrowIfNull(color);

            tlpMain.BackColor = color;
            lblTime.BackColor = color;
            messageDisplayStrategy.SetBackColor(color);

            if (Debugger.IsAttached)
            {
                messageDisplayStrategy.SetBackColor(Color.Coral);
                lblTime.BackColor = Color.Brown;
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            DrawRoundedRectangle(e.Graphics);
        }

        protected override void OnSizeChanged(EventArgs e)
        {
            base.OnSizeChanged(e);
            AdjustHeight();
            Invalidate(); // Redraw the control to update rounded rectangle on resize
        }

        private void DrawRoundedRectangle(Graphics graphics)
        {
            ArgumentNullException.ThrowIfNull(graphics);

            Color borderColor = ChatMessageDto.SenderId.Equals(_session.User.ClientId) ? MessageColors.MessageBoard2 : MessageColors.MessageBoard1;
            Color fillColor = ChatMessageDto.SenderId.Equals(_session.User.ClientId) ? MessageColors.MessageBackColor2 : MessageColors.MessageBackColor1;

            GraphicsPath path = CreateRoundedRectanglePath();

            graphics.SmoothingMode = SmoothingMode.HighQuality;
            using (var fillBrush = new SolidBrush(fillColor))
            {
                graphics.FillPath(fillBrush, path);
                SetBackColor(fillColor);
            }

            using var borderPen = new Pen(borderColor, BorderWidth);
            graphics.DrawPath(borderPen, path);
        }

        private GraphicsPath CreateRoundedRectanglePath()
        {
            var path = new GraphicsPath();
            path.AddArc(0, 0, BorderRadius, BorderRadius, 180, 90);
            path.AddArc(Width - BorderRadius - 1, 0, BorderRadius, BorderRadius, 270, 90);
            path.AddArc(Width - BorderRadius - 1, Height - BorderRadius - 1, BorderRadius, BorderRadius, 0, 90);
            path.AddArc(0, Height - BorderRadius - 1, BorderRadius, BorderRadius, 90, 90);
            path.CloseAllFigures();
            return path;
        }

        private void AdjustHeight()
        {
            if (ChatMessageDto == null || messageDisplayStrategy == null || lblTime == null) return;

            int textMessageHeight = messageDisplayStrategy.CalculateHeight();
            int lblTimeHeight = 15;
            Height = textMessageHeight + lblTimeHeight + 10;
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Dispose managed resources
                lblTime?.Dispose();
                tlpMain?.Dispose();
            }

            if (disposing && (components != null))
            {
                components.Dispose();
            }

            base.Dispose(disposing);
        }
    }
}
