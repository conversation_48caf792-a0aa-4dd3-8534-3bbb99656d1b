﻿namespace SignalRChat.Core.Client.Modules.Group.CreateGroup
{
    public delegate void CreateGroupRequestedEventHandler(CreateGroupRequest? request);
    public delegate void CreateGroupReceivedEventHandler(CreateGroupResponse? response);

    public record CreateGroupRequest
    {
        public Ulid GroupId { get; set; }
        public string GroupName { get; set; }
        public byte[] Photo { get; set; }
    }

    public record CreateGroupResponse
    {
        public GroupDto Group { get; set; }
    }

    public interface ICreateGroupService : IBaseModuleService<CreateGroupRequest, CreateGroupResponse>
    {
        event CreateGroupReceivedEventHandler OnGroupCreated;
        Task<bool> SendAsync(string groupName, byte[] photo);
    }

    public class CreateGroupService : ICreateGroupService
    {
        private readonly ISignalRConnectionManager _signalRConnectionManager;
        private readonly ISession _session;
        private readonly ILogger _logger;

        public event CreateGroupReceivedEventHandler OnGroupCreated;

        public CreateGroupService(ISignalRConnectionManager signalRConnectionManager, ISession session, ILogger logger)
        {
            _signalRConnectionManager = signalRConnectionManager;
            _session = session;
            _logger = logger;

            _signalRConnectionManager.OnCreateGroupReceived += Receive;
        }

        public async Task<bool> SendAsync(string groupName, byte[] photo)
        {
            if (string.IsNullOrEmpty(groupName))
                return false;

            try
            {
                _logger.Information($"[{nameof(CreateGroupService)}].[{nameof(CreateGroup)}] => Start Creating Group [{groupName}]");
                var request = new CreateGroupRequest()
                {
                    GroupId = UlidGenerator.Generator(groupName),
                    GroupName = groupName,
                    Photo = photo,
                };
                bool result = await SendAsync(request);
                _logger.Information($"[{nameof(CreateGroupService)}].[{nameof(CreateGroup)}] => End Creating Group [{groupName}]");
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"[{nameof(CreateGroupService)}].[{nameof(CreateGroup)}] => Creating Group");
                return false;
            }
        }

        public bool CanSend(CreateGroupRequest? request)
        {
            return
                _signalRConnectionManager.IsConnected
                && _signalRConnectionManager.IsLoggedIn;
        }

        public async Task<bool> SendAsync(CreateGroupRequest request)
        {
            try
            {
                _logger.Information($"[{nameof(CreateGroupService)}].[{nameof(SendAsync)}] => {nameof(request.GroupName)}: {request.GroupName}{(request.Photo is null ? string.Empty : $", {nameof(request.Photo)}: {request.Photo.Length}")}");
                await _signalRConnectionManager.InvokeCoreAsync("CreateGroup", [request]);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"[{nameof(CreateGroupService)}].[{nameof(SendAsync)}] => Failed Creating Group");
                return false;
            }
        }

        public void Receive(CreateGroupResponse? response)
        {
            if (response is null)
                return;

            _logger.Information($"[{nameof(CreateGroupService)}].[{nameof(CreateGroup)}] => Start Creating Group [{response.Group.Name}]");
            OnGroupCreated?.Invoke(response);

            var ptp = _session.GetChatCard(response.Group.ClientId);
            if (_signalRConnectionManager.IsLoggedIn && ptp == null)
            {
                var chatCard = new ChatCard(response.Group);
                foreach (var user in response.Group.Users)
                {
                    var parts = _session.GetChatCard(user.ClientId);
                    if (parts != null)
                        chatCard.GroupChatCard.Add(parts);
                }
                _session.ChatCards.TryAdd(chatCard);
            }
            _logger.Information($"[{nameof(CreateGroupService)}].[{nameof(CreateGroup)}] => End Creating Group [{response.Group.Name}]");
        }
    }
}
