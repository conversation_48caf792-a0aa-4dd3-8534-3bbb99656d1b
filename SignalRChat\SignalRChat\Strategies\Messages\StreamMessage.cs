﻿namespace SignalRChat.Strategies.Messages
{
    public class StreamMessage : PictureBox, IStreamMessageView, IDisposable
    {
        public StreamMessage()
        {
            this.SafelyInvokeAction(() =>
            {
                Dock = DockStyle.Left;
                SizeMode = PictureBoxSizeMode.Zoom;
            });

            DoubleClick += PictureBox_DoubleClick;
        }

        private void PictureBox_DoubleClick(object? sender, EventArgs e)
        {
            ShowImageInFullScreen();
        }

        private void ShowImageInFullScreen()
        {
            using var frm = new Form();
            frm.WindowState = FormWindowState.Maximized;
            frm.TopMost = true;
            frm.StartPosition = FormStartPosition.CenterScreen;

            using var tlp = new TableLayoutPanel();
            frm.Controls.Add(tlp);
            tlp.Dock = DockStyle.Fill;
            tlp.ColumnCount = 1;
            tlp.RowCount = 1;

            using var pbx = new PictureBox();
            pbx.Image = Image;
            pbx.Dock = DockStyle.Fill;
            pbx.SizeMode = PictureBoxSizeMode.Zoom;
            tlp.Controls.Add(pbx, 0, 0);
            frm.ShowDialog();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Unsubscribe from events
                DoubleClick -= PictureBox_DoubleClick;
            }

            base.Dispose(disposing);
        }
    }
}
