namespace SignalRChat.Core.Client.Modules.ChatMessage.StreamMessage
{
    public delegate void StreamMessageRequestedEventHandler(StreamMessageRequest? request);
    public delegate void StreamMessageReceivedEventHandler(StreamMessageResponse? response);

    public interface IStreamMessageService : IBaseModuleService<StreamMessageRequest, StreamMessageResponse>
    {
        event StreamMessageReceivedEventHandler OnNewStreamMessage;
        bool IsStreaming { get; set; }
        Task<bool> SendAsync();
        Task StopStreaming();
    }

    public class StreamMessageService : BaseChatMessage, IStreamMessageService
    {
        private readonly SemaphoreSlim locker = new(1);

        public event StreamMessageReceivedEventHandler OnNewStreamMessage;
        public bool IsStreaming { get; set; } = false;

        public StreamMessageService(
            ISignalRConnectionManager signalRConnectionManager,
            ISendChatMessageManager sendChatMessageManager,
            IReceiveChatMessageManager receiveChatMessageManager,
            ISession session,
            ILogger logger) : base(signalRConnectionManager, sendChatMessageManager, receiveChatMessageManager, session, logger)
        {
            _signalRConnectionManager.OnStreamMessageReceived += Receive;
        }

        public async Task<bool> SendAsync()
        {
            IsStreaming = true;
            var request = new StreamMessageRequest(
                UlidGenerator.Generator(),
                _session.User.ClientId,
                _session.SelectedChatCard.ClientId,
                UlidGenerator.Generator()
            );

            var streamChatMessageDto = new StreamChatMessageDto(request);
            _session.SelectedChatCard.ChatMessages.Add(streamChatMessageDto);

            await Task.Run(async () =>
            {
                while (IsStreaming)
                {
                    await locker.WaitAsync();
                    try
                    {
                        var screenShot = new ScreenShot();
                        screenShot = screenShot.GerScreenShot(ScreenCapture.CaptureDesktop());

                        request.Stream = screenShot.ScreenShotBitmapArray;

#pragma warning disable CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed
                        SendAsync(request);
#pragma warning restore CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed
                    }
                    catch (Exception ex)
                    {
                        _logger.Error(ex, $"[{nameof(StreamMessageService)}].[{nameof(SendAsync)}] => Sending Stream Message");
                    }
                    locker.Release();
                }
            });
            IsStreaming = false;
            return true;
        }

        public bool CanSend(StreamMessageRequest? request)
        {
            return base.CanSend(request);
        }

        public async Task<bool> SendAsync(StreamMessageRequest request)
        {
            return await _sendChatMessageManager.SendAsync(request);
        }

        public async void Receive(StreamMessageResponse? response)
        {
            if (response is null || response.Stream is null || response.Stream.Length == 0)
                return;

            OnNewStreamMessage?.Invoke(response);
            await _receiveChatMessageManager.ReceiveAsync(response);
        }

        public Task StopStreaming()
        {
            if (IsStreaming)
                IsStreaming = false;
            return Task.CompletedTask;
        }
    }
}
