﻿namespace SignalRChat.CustomControls
{
    public class ContactCardList : TableLayoutPanel, IDisposable
    {
        private readonly IServiceProvider _serviceProvider;
        private IList<ContactCard> _contactCardsList;
        private ContactCard _selectedContactCard;
        private BindingList<ChatCard> _dataSource;

        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public BindingList<ChatCard> DataSource
        {
            get => _dataSource;
            set
            {
                if (_dataSource != null)
                {
                    _dataSource.ListChanged -= DataSource_ListChanged;
                }

                _dataSource = value;
                SetContactCards(value);

                if (_dataSource != null)
                {
                    _dataSource.ListChanged += DataSource_ListChanged;
                }
            }
        }

        public delegate void ContactCardSelectedEventHandler(ContactCard card);
        public event ContactCardSelectedEventHandler ContactCardSelected;

        public ContactCardList(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _contactCardsList = new List<ContactCard>();
            AutoScrollMinSize = new Size(30, 0);
            AutoScroll = true;
            RowCount = 0;
            RowStyles.Clear();
        }

        private void ChatList_ContactCardClicked(object? sender)
        {
            if (sender is ContactCard contactCard)
            {
                if (_selectedContactCard != null)
                {
                    _selectedContactCard.IsSelected = false;
                }

                _selectedContactCard = contactCard;
                ContactCardSelected?.Invoke(contactCard);
                _selectedContactCard.IsSelected = true;
            }
        }

        private void DataSource_ListChanged(object? sender, ListChangedEventArgs e)
        {
            this.SafelyInvokeAction(() =>
            {
                switch (e.ListChangedType)
                {
                    case ListChangedType.Reset:
                        Clear();
                        break;
                    case ListChangedType.ItemAdded:
                        Add(new ContactCard(DataSource[e.NewIndex], _serviceProvider));
                        break;
                    case ListChangedType.ItemDeleted:
                        RemoveAt(e.NewIndex);
                        break;
                        // Handle other cases if needed
                }
            });
        }

        public void Add(ContactCard item)
        {
            if (item == null) throw new ArgumentNullException(nameof(item));

            item.ContactCardClicked += ChatList_ContactCardClicked;
            _contactCardsList.Add(item);
            SetColumnSpan(item, 2);

            RowStyle temp;
            if (RowCount > 0)
            {
                temp = RowStyles[RowCount - 1];
                temp.SizeType = SizeType.AutoSize;
                temp.Height = 20;
            }
            else
            {
                temp = new RowStyle(SizeType.AutoSize, 20);
                RowCount++;
                RowStyles.Add(temp);
            }
            Controls.Add(item, 0, RowCount - 1);
            RowCount++;
            RowStyles.Add(new RowStyle(SizeType.Percent, 100));
        }

        public void Clear()
        {
            foreach (var contactCard in _contactCardsList)
            {
                contactCard.ContactCardClicked -= ChatList_ContactCardClicked;
            }

            _contactCardsList.Clear();
            Controls.Clear();
        }

        public bool Remove(ContactCard item)
        {
            if (item == null) throw new ArgumentNullException(nameof(item));

            item.ContactCardClicked -= ChatList_ContactCardClicked;
            Controls.Remove(item);
            return _contactCardsList.Remove(item);
        }

        public void RemoveAt(int index)
        {
            if (index < 0 || index >= _contactCardsList.Count) throw new ArgumentOutOfRangeException(nameof(index));

            var item = _contactCardsList[index];
            item.ContactCardClicked -= ChatList_ContactCardClicked;
            _contactCardsList.RemoveAt(index);
            Controls.RemoveAt(index);
        }

        private IList<ChatCard> GetChatCards(IList<ContactCard> contactCards)
        {
            return contactCards.Select(contactCard => contactCard.ChatCard).ToList();
        }

        private void SetContactCards(IList<ChatCard> chatCards)
        {
            Clear();
            foreach (var chatCard in chatCards)
            {
                var contactCard = new ContactCard(chatCard, _serviceProvider);
                Add(contactCard);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (_dataSource != null)
                {
                    _dataSource.ListChanged -= DataSource_ListChanged;
                }

                Clear();
            }

            base.Dispose(disposing);
        }
    }
}
