﻿namespace SignalRChat.Core.Server.DI
{
    public static class ServiceCollectionExtension
    {
        public static IServiceCollection AddSignalRChatServer(this IServiceCollection services, Action<DbContextOptionsBuilder>? dbContextOptionsAction = null)
            => services.AddSignalRChatServer(Logger.CreateSerilog(), dbContextOptionsAction);

        public static IServiceCollection AddSignalRChatServer(this IServiceCollection services, ILogger logger, Action<DbContextOptionsBuilder>? dbContextOptionsAction = null)
        {
            services.AddSerilog(logger);

            if (dbContextOptionsAction != null)
            {
                services.AddDbContext<ApplicationDbContext>(options =>
                {
                    dbContextOptionsAction(options);
                    options.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
                });
            }
            else
            {
                services.AddDbContext<ApplicationDbContext>(options =>
                {
                    options.UseInMemoryDatabase("SignalRChatDb");
                    options.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
                });
            }

            services.AddScoped<IRepository, EFCoreRepository>();

            services.AddSignalR(options =>
            {
                options.EnableDetailedErrors = true;
                options.MaximumReceiveMessageSize = null;
            }).AddJsonProtocol(options =>
            {
                options.PayloadSerializerOptions.PropertyNamingPolicy = null;
                options.PayloadSerializerOptions.IgnoreReadOnlyFields = true;
                options.PayloadSerializerOptions.IgnoreReadOnlyProperties = true;
                options.PayloadSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
            });

            return services;
        }
    }
}
