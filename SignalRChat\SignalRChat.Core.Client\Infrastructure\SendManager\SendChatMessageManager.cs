﻿namespace SignalRChat.Core.Client.Infrastructure.SendManager
{
    public class SendChatMessageManager(ISignalRConnectionManager signalRConnectionManager, ILogger logger) : ISendChatMessageManager
    {
        public async Task<bool> SendAsync(IChatMessageRequest? request)
        {
            try
            {
                if (request == null)
                    return false;

                logger.Information($"[{nameof(SendChatMessageManager)}].[{nameof(SendAsync)}] => Start Sending Message From [{request.SenderId}] To [{request.ReceiverId}]");
                switch (request)
                {
                    case TextMessageRequest textMessageRequest:
                        await signalRConnectionManager.InvokeCoreAsync(nameof(Modules.ChatMessage.TextMessage), [request]);
                        break;
                    case ImageMessageRequest imageMessageRequest:
                        await signalRConnectionManager.InvokeCoreAsync(nameof(Modules.ChatMessage.ImageMessage), [request]);
                        break;
                    case RecordMessageRequest recordMessageRequest:
                        await signalRConnectionManager.InvokeCoreAsync(nameof(Modules.ChatMessage.RecordMessage), [request]);
                        break;
                    case StreamMessageRequest streamMessageRequest:
                        await signalRConnectionManager.InvokeCoreAsync(nameof(Modules.ChatMessage.StreamMessage), [request]);
                        break;
                    case BuzzMessageRequest buzzMessageRequest:
                        await signalRConnectionManager.InvokeCoreAsync(nameof(Modules.ChatMessage.BuzzMessage), [request]);
                        break;
                    case ParticipantTypingRequest participantTypingRequest:
                        await signalRConnectionManager.InvokeCoreAsync(nameof(Modules.ChatMessage.ParticipantTyping), [request]);
                        break;
                    default:
                        logger.Warning($"[{nameof(SendChatMessageManager)}].[{nameof(SendAsync)}] => Unknown message type");
                        return false;
                }
                logger.Information($"[{nameof(SendChatMessageManager)}].[{nameof(SendAsync)}] => End Sending Message From [{request.SenderId}] To [{request.ReceiverId}]");
                return true;
            }
            catch (Exception ex)
            {
                logger.Error(ex, $"[{nameof(SendChatMessageManager)}].[{nameof(SendAsync)}] => Failed Sending Message");
                return false;
            }
        }
    }
}
