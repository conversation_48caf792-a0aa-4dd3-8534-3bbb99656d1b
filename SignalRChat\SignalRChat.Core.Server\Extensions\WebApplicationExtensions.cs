﻿namespace SignalRChat.Core.Server.Extensions
{
    public static class WebApplicationExtensions
    {
        public static IApplicationBuilder UseSignalRChat<HubClass>(this IApplicationBuilder app) where HubClass : Hub/*<ISignalRChatHub>*/
        {
            app.UseSignalRChat<HubClass>("/signalchat");

            return app;
        }

        public static IApplicationBuilder UseSignalRChat<HubClass>(this IApplicationBuilder app, string hubRoutePath) where HubClass : Hub/*<ISignalRChatHub>*/
        {
            app.UseEndpoints(endpoints =>
            {
                string hubPath = hubRoutePath.StartsWith('/') ? hubRoutePath : "/" + hubRoutePath;

                endpoints.MapHub<HubClass>(hubPath);
            });

            return app;
        }
    }
}
