﻿namespace SignalRChat.CustomControls
{
    public partial class ContactCard : User<PERSON>ontrol, IDisposable
    {
        public readonly ChatCard ChatCard;
        public readonly Chat Chat;

        private bool _isSelected = false;
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                _isSelected = value;
                SetIsSelected(value);
            }
        }

        public delegate void ContactCardClickedEventHandler(object sender);
        public event ContactCardClickedEventHandler ContactCardClicked;

        public ContactCard(ChatCard chatCard, IServiceProvider serviceProvider)
        {
            ChatCard = chatCard ?? throw new ArgumentNullException(nameof(chatCard));
            Chat = new Chat(chatCard, serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider)));

            InitializeComponent();
            Dock = DockStyle.Fill;

            ChatCard.PropertyChanged += Participant_PropertyChanged;

            RefreshValues();

            pbxIsSelected.Click += OnContactCardClicked;
            pbxPhoto.Click += OnContactCardClicked;
            lblName.Click += OnContactCardClicked;
            pbxIsTypingOrHasSentNewMessage.Click += OnContactCardClicked;
            pbxIsOnline.Click += OnContactCardClicked;
            tlpMain.Click += OnContactCardClicked;
            this.Click += OnContactCardClicked;

            pbxIsOnline.Paint += PbxIsOnline_Paint;
        }

        private void OnContactCardClicked(object? sender, EventArgs e)
        {
            ContactCardClicked?.Invoke(this);
        }

        private void PbxIsOnline_Paint(object? sender, PaintEventArgs e)
        {
            Color color = ChatCard.IsLoggedIn ? ContactCardColors.IsOnline2 : ContactCardColors.IsOnline1;

            int dotDiameter = 10; // Diameter of the dot
            int centerX = pbxIsOnline.Size.Width / 2;
            int centerY = pbxIsOnline.Size.Height / 2;
            int dotX = centerX - dotDiameter / 2;
            int dotY = centerY - dotDiameter / 2;

            using (SolidBrush brush = new SolidBrush(color))
            {
                e.Graphics.FillEllipse(brush, dotX, dotY, dotDiameter, dotDiameter);
            }
        }

        private void Participant_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            RefreshValues();
        }

        private void RefreshValues()
        {
            this.SafelyInvokeAction(() =>
            {
                pbxPhoto.Image = Converters.ByteArrayToImage(ChatCard.Photo);
                lblName.Text = ChatCard.Name;
                pbxIsTypingOrHasSentNewMessage.Visible = ChatCard.IsTyping || ChatCard.HasSentNewMessage;
                SetHasSentNewMessage(ChatCard.HasSentNewMessage);
                SetIsTyping(ChatCard.IsTyping);
                SetIsOnline(ChatCard.IsLoggedIn);
            });
        }

        private void SetIsSelected(bool isSelected)
        {
            this.SafelyInvokeAction(() =>
            {
                pbxIsSelected.BackColor = isSelected ? ContactCardColors.IsSelected1 : ContactCardColors.IsSelected2;
            });
        }

        private void SetHasSentNewMessage(bool hasSentNewMessage)
        {
            this.SafelyInvokeAction(() =>
            {
                pbxIsTypingOrHasSentNewMessage.Visible = hasSentNewMessage || ChatCard.IsTyping;
                pbxIsTypingOrHasSentNewMessage.Image = hasSentNewMessage
                    ? Properties.Resources.unread_messages_icon
                    : ChatCard.IsTyping
                        ? Properties.Resources.writing_icon
                        : null;
            });
        }

        private void SetIsTyping(bool isTyping)
        {
            this.SafelyInvokeAction(() =>
            {
                pbxIsTypingOrHasSentNewMessage.Visible = isTyping || ChatCard.HasSentNewMessage;
                pbxIsTypingOrHasSentNewMessage.Image = isTyping
                    ? Properties.Resources.writing_icon
                    : ChatCard.HasSentNewMessage
                        ? Properties.Resources.unread_messages_icon
                        : null;
            });
        }

        private void SetIsOnline(bool isOnline)
        {
            pbxIsOnline.Invalidate();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Unsubscribe from events
                ChatCard.PropertyChanged -= Participant_PropertyChanged;

                pbxIsSelected.Click -= OnContactCardClicked;
                pbxPhoto.Click -= OnContactCardClicked;
                lblName.Click -= OnContactCardClicked;
                pbxIsTypingOrHasSentNewMessage.Click -= OnContactCardClicked;
                pbxIsOnline.Click -= OnContactCardClicked;
                tlpMain.Click -= OnContactCardClicked;
                this.Click -= OnContactCardClicked;

                pbxIsOnline.Paint -= PbxIsOnline_Paint;

                // Dispose managed resources
                pbxIsSelected.Dispose();
                pbxPhoto.Dispose();
                lblName.Dispose();
                pbxIsTypingOrHasSentNewMessage.Dispose();
                pbxIsOnline.Dispose();
                tlpMain.Dispose();
            }

            if (disposing && (components != null))
            {
                components.Dispose();
            }

            base.Dispose(disposing);
        }
    }
}
