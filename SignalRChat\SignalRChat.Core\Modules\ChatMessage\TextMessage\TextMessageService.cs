﻿namespace SignalRChat.Core.Modules.ChatMessage.TextMessage
{
    public record TextMessageRequest(Ulid MessageId, Ulid SenderId, Ulid ReceiverId, string Message) : BaseChatMessageRequest(MessageId, SenderId, ReceiverId)
    {
        public override bool IsValid()
        {
            return
                base.IsValid()
                && !string.IsNullOrEmpty(Message);
        }
    }

    public record TextMessageResponse(Ulid MessageId, Ulid SenderId, Ulid ReceiverId, string Message) : BaseChatMessageResponse(MessageId, SenderId, ReceiverId, true);
}
