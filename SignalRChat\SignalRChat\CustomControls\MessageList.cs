﻿namespace SignalRChat.CustomControls
{
    public class MessageList : TableLayoutPanel, IDisposable
    {
        private IList<Message> _messagesList;
        private readonly IServiceProvider _serviceProvider;
        private readonly ISession _session;
        private BindingList<ChatMessageDto> _dataSource;

        public MessageList(BindingList<ChatMessageDto> chatMessages, IServiceProvider serviceProvider)
        {
            _messagesList = [];
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _session = _serviceProvider.GetRequiredService<ISession>();
            SetDataSource(chatMessages);

            ColumnStyles.Clear();
            ColumnCount = 3;
            ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));
            ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));
            ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            DoubleBuffered = true; // Enable double buffering

            Dock = DockStyle.Fill;

            AutoScroll = true;
            HScroll = false;
            VScroll = false;
        }

        protected override void OnCellPaint(TableLayoutCellPaintEventArgs e)
        {
            base.OnCellPaint(e);
            if (Debugger.IsAttached)
            {
                using var b = new SolidBrush(Color.Red);
                if ((e.Column + e.Row) % 2 == 1)
                {
                    e.Graphics.FillRectangle(Brushes.LightBlue, e.CellBounds);
                    e.Graphics.DrawString(e.CellBounds.Height.ToString(), Font, b, e.CellBounds);
                }
                else
                {
                    e.Graphics.FillRectangle(Brushes.LightYellow, e.CellBounds);
                    e.Graphics.DrawString(e.CellBounds.Height.ToString(), Font, b, e.CellBounds);
                }
            }
        }

        protected override void OnControlAdded(ControlEventArgs e)
        {
            base.OnControlAdded(e);
            AdjustRowHeights();
        }

        protected override void OnResize(EventArgs eventargs)
        {
            base.OnResize(eventargs);
            AdjustRowHeights();
        }

        private void AdjustRowHeights()
        {
            PerformLayout(); // Force layout adjustment
            Invalidate(); // Force the control to redraw
        }

        private void SetDataSource(BindingList<ChatMessageDto> chatMessageDtos)
        {
            Clear();
            _messagesList = [];
            _dataSource = chatMessageDtos ?? throw new ArgumentNullException(nameof(chatMessageDtos));
            foreach (ChatMessageDto chatMessageDto in chatMessageDtos)
                Add(new Message(chatMessageDto, _serviceProvider));
            _dataSource.ListChanged += DataSource_ListChanged;
        }

        private void DataSource_ListChanged(object? sender, ListChangedEventArgs e)
        {
            this.SafelyInvokeAction(() =>
            {
                switch (e.ListChangedType)
                {
                    case ListChangedType.Reset:
                        Clear();
                        break;
                    case ListChangedType.ItemAdded:
                        Add(new Message(_dataSource[e.NewIndex], _serviceProvider));
                        break;
                    case ListChangedType.ItemDeleted:
                        RemoveAt(e.NewIndex);
                        break;
                    case ListChangedType.ItemMoved:
                        break;
                    case ListChangedType.ItemChanged:
                        {
                            if (_dataSource[e.NewIndex] is StreamChatMessageDto chat)
                            {
                                var message = _messagesList.SingleOrDefault(x => x.ChatMessageDto.SenderId.Equals(_session.User.ClientId) == false && x.ChatMessageDto is StreamChatMessageDto streamChatMessageDto && streamChatMessageDto.StreamId.Equals(chat.StreamId));
                                message?.UpdateDisplayMessage(chat);
                            }
                        }
                        break;
                    case ListChangedType.PropertyDescriptorAdded:
                        break;
                    case ListChangedType.PropertyDescriptorDeleted:
                        break;
                    case ListChangedType.PropertyDescriptorChanged:
                        break;
                }
            });
        }

        public void Add(Message item)
        {
            ArgumentNullException.ThrowIfNull(item);

            _messagesList.Add(item);
            this.SafelyInvokeAction(() => item.Dock = DockStyle.Fill);
            int colIndex = item.ChatMessageDto.SenderId.Equals(_session.User.ClientId) ? 0 : 1;
            this.SafelyInvokeAction(() => SetColumnSpan(item, 2));
            var temp = new RowStyle(SizeType.AutoSize);
            this.SafelyInvokeAction(() => RowCount++);
            this.SafelyInvokeAction(() => RowStyles.Add(temp));
            this.SafelyInvokeAction(() => Controls.Add(item, colIndex, RowCount - 1));
            this.SafelyInvokeAction(() => RowCount++);
            this.SafelyInvokeAction(() => RowStyles.Add(new RowStyle(SizeType.AutoSize)));

            // Scroll to the newest message
            this.SafelyInvokeAction(() => VerticalScroll.Value = (int)RowStyles.OfType<RowStyle>().Sum(x => x.Height));
            this.SafelyInvokeAction(() => ScrollControlIntoView(item));
        }

        public void Clear()
        {
            Controls.Clear();
            _messagesList.Clear();
        }

        public bool Remove(Message item)
        {
            ArgumentNullException.ThrowIfNull(item);

            Controls.Remove(item);
            return _messagesList.Remove(item);
        }

        public void RemoveAt(int index)
        {
            if (index < 0 || index >= _messagesList.Count) throw new ArgumentOutOfRangeException(nameof(index));

            Controls.RemoveAt(index);
            _messagesList.RemoveAt(index);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Unsubscribe from events
                if (_dataSource != null)
                {
                    _dataSource.ListChanged -= DataSource_ListChanged;
                }

                // Dispose managed resources
                foreach (var message in _messagesList)
                {
                    message.Dispose();
                }
            }

            base.Dispose(disposing);
        }
    }
}
