﻿namespace SignalRChat.Core.Server.Hubs
{
    public class SignalRChatHub(IRepository repository, ILogger logger) : Hub<ISignalRChatHub>
    {
        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            try
            {
                var clientId = GetUserName;
                if (clientId.IsValid())
                {
                    await Clients.Others.ParticipantDisconnection(clientId.Value);
                    logger.Information($"[{nameof(SignalRChatHub)}].[{nameof(OnDisconnectedAsync)}] <> {clientId} Disconnected");
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex, "Error on disconnect");
            }
            await base.OnDisconnectedAsync(exception);
        }

        public override async Task OnConnectedAsync()
        {
            try
            {
                var clientId = GetUserName;
                UserDto? user = await GetUser(clientId, false);
                if (user != null)
                {
                    user.UpdateConnectionId(Context.ConnectionId);
                    await repository.UpdateClient(user);
                    await Clients.Others.ParticipantReconnection(user.ClientId);
                    logger.Information($"[{nameof(SignalRChatHub)}].[{nameof(OnConnectedAsync)}] == {user.Name} Reconnected");
                }
                logger.Information($"[{nameof(SignalRChatHub)}].[{nameof(OnConnectedAsync)}] == {clientId} Connected");
            }
            catch (Exception ex)
            {
                logger.Error(ex, "Error on connect");
            }
            await base.OnConnectedAsync();
        }

        public virtual async Task<LoginResponse?> Login(LoginRequest request)
        {
            if (string.IsNullOrEmpty(request.Name))
                return null;

            logger.Information($"[{nameof(SignalRChatHub)}].[{nameof(Login)}] ++ {request.Name} Logged In");

            UserDto? user = await GetUser(request.Id, true);
            if (user == null)
            {
                user = new UserDto(request.Id, Context.ConnectionId, request.Name, request.Photo);
                var added = await repository.AddClient(user);
                if (!added)
                    await repository.UpdateClient(user);
            }
            await Clients.Others.ParticipantLogin(user);

            var users = (await repository.GetUsersWithMessages()).Where(x => !x.ClientId.Equals(request.Id));
            var groups = (await repository.GetGroupsWithMessages()).Where(x => x.Users != null && x.Users.Any(xx => xx.ClientId == request.Id));

            List<ClientDto> clients = [.. users, .. groups];

            var response = new LoginResponse(user, clients);
            return response;
        }

        public virtual async Task<bool> Logout(LogoutRequest request)
        {
            var clientId = GetUserName;
            if (clientId.IsValid())
            {
                UserDto? user = await GetUser(clientId, false);
                if (user != null)
                {
                    await Clients.Others.ParticipantLogout(clientId.Value);
                    await RemoveUser(clientId);
                    logger.Information($"[{nameof(SignalRChatHub)}].[{nameof(Logout)}] -- {clientId} Logged Out");
                    return true;
                }
            }
            return false;
        }

        private async Task<bool> SendChatMessage(IChatMessageRequest request)
        {
            var result = false;
            ClientDto? receiver = await GetReceiver(request);
            if (receiver != null)
            {
                logger.Information($"Start Sending message of type: {request.GetType().Name} from {request.SenderId} to {request.ReceiverId}");
                switch (request)
                {
                    case TextMessageRequest textMessageRequest:
                        var textMessageResponse = new TextMessageResponse(textMessageRequest.MessageId, textMessageRequest.SenderId, textMessageRequest.ReceiverId, textMessageRequest.Message);
                        result = await SendToReceiver(receiver, textMessageResponse, (client, msg) => client.TextMessage(msg));
                        break;
                    case ImageMessageRequest imageMessageRequest:
                        var imageMessageResponse = new ImageMessageResponse(imageMessageRequest.MessageId, imageMessageRequest.SenderId, imageMessageRequest.ReceiverId, imageMessageRequest.Image);
                        result = await SendToReceiver(receiver, imageMessageResponse, (client, msg) => client.ImageMessage(msg));
                        break;
                    case RecordMessageRequest recordMessageRequest:
                        var recordMessageResponse = new RecordMessageResponse(recordMessageRequest.MessageId, recordMessageRequest.SenderId, recordMessageRequest.ReceiverId, recordMessageRequest.Record);
                        result = await SendToReceiver(receiver, recordMessageResponse, (client, msg) => client.RecordMessage(msg));
                        break;
                    case StreamMessageRequest streamMessageRequest:
                        var streamMessageResponse = new StreamMessageResponse(streamMessageRequest.MessageId, streamMessageRequest.SenderId, streamMessageRequest.ReceiverId, streamMessageRequest.StreamId, streamMessageRequest.Stream);
                        result = await SendToReceiver(receiver, streamMessageResponse, (client, msg) => client.StreamMessage(msg));
                        break;
                    case BuzzMessageRequest buzzMessageRequest:
                        var buzzMessageResponse = new BuzzMessageResponse(buzzMessageRequest.MessageId, buzzMessageRequest.SenderId, buzzMessageRequest.ReceiverId);
                        result = await SendToReceiver(receiver, buzzMessageResponse, (client, msg) => client.BuzzMessage(msg));
                        break;
                    case ParticipantTypingRequest participantTypingRequest:
                        var participantTypingResponse = new ParticipantTypingResponse(participantTypingRequest.MessageId, participantTypingRequest.SenderId, participantTypingRequest.ReceiverId);
                        result = await SendToReceiver(receiver, participantTypingResponse, (client, msg) => client.ParticipantTyping(msg));
                        break;
                    default:
                        logger.Warning($"Unhandled message type: {request.GetType().Name}");
                        break;
                }
                logger.Information($"End Sending message of type: {request.GetType().Name} from {request.SenderId} to {request.ReceiverId}");
            }
            return result;
        }

        private async Task<bool> SendToReceiver<TResponse>(ClientDto receiver, TResponse response, Func<ISignalRChatHub, TResponse, Task> sendFunc)
            where TResponse : IChatMessageResponse
        {
            var result = await repository.AddMessage(response);
            if (result || !response.Savable)
            {
                switch (receiver.ChatType)
                {
                    case ChatType.IndividualChat:
                        await sendFunc(Clients.Client(receiver.ConnectionId), response);
                        break;
                    case ChatType.GroupChat:
                        await sendFunc(Clients.OthersInGroup(receiver.ConnectionId), response);
                        break;
                }
            }
            return result;
        }

        public virtual async Task<bool> TextMessage(TextMessageRequest request)
        {
            return await SendChatMessage(request);
        }

        public virtual async Task<bool> ImageMessage(ImageMessageRequest request)
        {
            return await SendChatMessage(request);
        }

        public virtual async Task<bool> RecordMessage(RecordMessageRequest request)
        {
            return await SendChatMessage(request);
        }

        public virtual async Task<bool> StreamMessage(StreamMessageRequest request)
        {
            return await SendChatMessage(request);
        }

        public virtual async Task<bool> BuzzMessage(BuzzMessageRequest request)
        {
            return await SendChatMessage(request);
        }

        public virtual async Task<bool> ParticipantTyping(ParticipantTypingRequest request)
        {
            return await SendChatMessage(request);
        }

        public virtual async Task<GroupDto?> CreateGroup(CreateGroupRequest request)
        {
            UserDto? user = await GetUser(GetUserName, false);
            if (user != null)
            {
                logger.Information($"[{nameof(SignalRChatHub)}].[{nameof(CreateGroup)}] => Start Create [{request.GroupName}] Group");
                GroupDto? group = await GetGroup(request.GroupId, false);
                if (group != null)
                {
                    var joinGroupRequest = new JoinGroupRequest(group.ClientId);
                    return await JoinGroup(joinGroupRequest);
                }
                else
                {
                    group = new GroupDto(request.GroupId, request.GroupName, request.GroupName, request.Photo, [user]);
                    var added = await repository.AddClient(group);
                    if (!added) return null;
                    await Groups.AddToGroupAsync(user.ConnectionId, group.Name);
                    var response = new CreateGroupResponse()
                    {
                        Group = group,
                    };
                    await Clients.Groups(request.GroupName).CreateGroup(response);
                }
                logger.Information($"[{nameof(SignalRChatHub)}].[{nameof(CreateGroup)}] => End Create [{request.GroupName}] Group");
                return group;
            }
            return null;
        }

        public virtual async Task<GroupDto?> JoinGroup(JoinGroupRequest request)
        {
            GroupDto? group = await GetGroup(request.GroupId, false);
            if (group != null && group.ChatType == ChatType.GroupChat)
            {
                logger.Information($"[{nameof(SignalRChatHub)}].[{nameof(JoinGroup)}] => Start {GetUserName} Join To {group.Name} Group");
                UserDto? user = await GetUser(GetUserName, false);
                if (user != null)
                {
                    group.Users.Add(user);
                    await Groups.AddToGroupAsync(user.ConnectionId, group.Name);
                    var response = new JoinGroupResponse(group, user);
                    await Clients.Groups(group.Name).JoinGroup(response);
                    logger.Information($"[{nameof(SignalRChatHub)}].[{nameof(JoinGroup)}] => End {GetUserName} Join To {group.Name} Group");
                }
            }
            return group;
        }

        public virtual async void LeaveGroup(LeaveGroupRequest request)
        {
            GroupDto? group = await GetGroup(request.GroupId, false);
            UserDto? user = group?.Users.SingleOrDefault(x => x.ClientId == GetUserName);
            if (group != null && group.ChatType == ChatType.GroupChat && user != null)
            {
                logger.Information($"[{nameof(SignalRChatHub)}].[{nameof(LeaveGroup)}] => Start {GetUserName} Leave From {user.Name} Group");
                group.Users.Remove(user);
                await Groups.RemoveFromGroupAsync(user.ConnectionId, group.Name);
                var response = new LeaveGroupResponse(group, user);
                await Clients.Groups(group.Name).LeaveGroup(response);
                if (group.Users.Count == 0)
                    await RemoveUser(group.ClientId);
                logger.Information($"[{nameof(SignalRChatHub)}].[{nameof(LeaveGroup)}] => End {GetUserName} Leave From {user.Name} Group");
            }
        }

        protected virtual Ulid? GetUserName => Ulid.TryParse(Context?.GetHttpContext()?.Request?.Headers["UserName"].FirstOrDefault(), out Ulid result) ? result : null;

        protected async Task<ClientDto?> GetClient(Ulid? clientId, bool withMessages)
        {
            if (!clientId.IsValid())
                return null;

            if (withMessages)
                return await repository.GetClientWithMessagesByType<Client, ClientDto>(clientId.Value);
            else
                return await repository.GetClientByType<Client, ClientDto>(clientId.Value);
        }

        protected async Task<UserDto?> GetUser(Ulid? userId, bool withMessages)
        {
            if (!userId.IsValid())
                return null;

            if (withMessages)
                return await repository.GetClientWithMessagesByType<User, UserDto>(userId.Value);
            else
                return await repository.GetClientByType<User, UserDto>(userId.Value);
        }

        protected async Task<GroupDto?> GetGroup(Ulid? groupId, bool withMessages)
        {
            if (!groupId.IsValid())
                return null;

            if (withMessages)
                return await repository.GetClientWithMessagesByType<Group, GroupDto>(groupId.Value);
            else
                return await repository.GetClientByType<Group, GroupDto>(groupId.Value);
        }

        protected async Task<TDto?> GetEntity<T, TDto>(Ulid? id, bool withMessages) where T : Client where TDto : ClientDto
        {
            if (!id.IsValid())
                return null;
            if (withMessages)
                return await repository.GetClientWithMessagesByType<T, TDto>(id.Value);
            else
                return await repository.GetClientByType<T, TDto>(id.Value);
        }

        private async Task RemoveUser(Ulid? clientId)
        {
            if (!clientId.IsValid())
                return;

            await repository.DeleteClient(clientId.Value);

            foreach (var group in await repository.GetGroups())
            {
                if (group.Users != null)
                {
                    foreach (var user in group.Users)
                    {
                        if (user.ClientId == clientId)
                        {
                            await Groups.RemoveFromGroupAsync(user.ConnectionId, group.Name);
                            var response = new LeaveGroupResponse(group, user);
                            await Clients.Groups(group.Name).LeaveGroup(response);
                            group.Users.Remove(user);
                        }
                    }
                }
            }
        }

        private async Task<ClientDto?> GetReceiver(IChatMessageRequest request)
        {
            if (request != null)
            {
                if (request.ReceiverId.IsValid())
                {
                    ClientDto? client = await GetClient(request.ReceiverId, false);
                    return client;
                }
            }
            return null;
        }
    }
}
