﻿namespace SignalRChat.Factories.Messages
{
    public class MessageFactory
    {
        public static IMessageDisplayStrategy CreateMessage(MessageType messageDisplayType)
        {
            return messageDisplayType switch
            {
                MessageType.Text => new TextMessageDisplayStrategy(new TextMessage()),
                MessageType.Image => new ImageMessageDisplayStrategy(new ImageMessage()),
                MessageType.Record => new RecordMessageDisplayStrategy(new RecordMessage()),
                MessageType.Stream => new StreamMessageDisplayStrategy(new StreamMessage()),
                _ => throw new NotImplementedException($"Need to implement the {nameof(IMessageDisplayStrategy)} interface"),
            };
        }
    }
}
