﻿namespace SignalRChat.Core.Client.Modules.ChatMessage.RecordMessage
{
    public delegate void RecordMessageRequestedEventHandler(RecordMessageRequest? request);
    public delegate void RecordMessageReceivedEventHandler(RecordMessageResponse? response);

    public interface IRecordMessageService : IBaseModuleService<RecordMessageRequest, RecordMessageResponse>
    {
        event RecordMessageReceivedEventHandler OnNewRecordMessage;
        bool IsRecoding { get; set; }
        Task<bool> SendAsync();
        Task<bool> StopRecording();
    }

    public class RecordMessageService : BaseChatMessage, IRecordMessageService
    {
        private readonly VoiceRecorder _voiceRecorder;

        public event RecordMessageReceivedEventHandler OnNewRecordMessage;
        public bool IsRecoding { get; set; } = false;

        public RecordMessageService(
            ISignalRConnectionManager signalRConnectionManager,
            ISendChatMessageManager sendChatMessageManager,
            IReceiveChatMessageManager receiveChatMessageManager,
            ISession session,
            ILogger logger) : base(signalRConnectionManager, sendChatMessageManager, receiveChatMessageManager, session, logger)
        {
            _voiceRecorder = new VoiceRecorder();

            _signalRConnectionManager.OnRecordMessageReceived += Receive;
        }

        public async Task<bool> SendAsync()
        {
            if (!IsRecoding)
            {
                _logger.Information($"[{nameof(RecordMessageService)}].[{nameof(SendAsync)}] => Initializing Recording Message");
                var recordsDirectory = Path.Combine(Environment.CurrentDirectory, "Record Messages");
                if (!Directory.Exists(recordsDirectory)) Directory.CreateDirectory(recordsDirectory);

                var recordsCount = Directory.EnumerateFiles(recordsDirectory).Count() + 1;
                var recordPath = Path.Combine(recordsDirectory, $"RECORD_{recordsCount}.wav");

                StartRecording(recordPath);
                return await Task.FromResult(true);
            }
            return await Task.FromResult(false);
        }

        public bool CanSend(RecordMessageRequest? request)
        {
            return base.CanSend(request);
        }

        public async Task<bool> SendAsync(RecordMessageRequest request)
        {
            bool result = await _sendChatMessageManager.SendAsync(request);
            if (result)
            {
                var recordChatMessage = new RecordChatMessageDto(request);
                _session.SelectedChatCard.ChatMessages.Add(recordChatMessage);
            }
            return result;
        }

        public async void Receive(RecordMessageResponse? response)
        {
            if (response is null || response.Record is null || response.Record.Length == 0)
                return;

            await _receiveChatMessageManager.ReceiveAsync(response);
            OnNewRecordMessage?.Invoke(response);
            //var recordsDirectory = Path.Combine(Environment.CurrentDirectory, "Record Messages");
            //if (!Directory.Exists(recordsDirectory)) Directory.CreateDirectory(recordsDirectory);

            //var recordsCount = Directory.EnumerateFiles(recordsDirectory).Count() + 1;
            //var recordPath = Path.Combine(recordsDirectory, $"RECORD_{recordsCount}.wav");
        }

        private void StartRecording(string path)
        {
            if (!IsRecoding)
            {
                try
                {
                    _logger.Information($"[{nameof(RecordMessageService)}].[{nameof(StartRecording)}] => Start Recording Record Message");
                    _voiceRecorder.StartRecord(path);
                    IsRecoding = true;
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, $"[{nameof(RecordMessageService)}].[{nameof(StartRecording)}] => Failed Recording Record Message");
                    IsRecoding = false;
                }
            }
        }

        public async Task<bool> StopRecording()
        {
            string path = string.Empty;
            if (IsRecoding)
            {
                _logger.Information($"[{nameof(RecordMessageService)}].[{nameof(StopRecording)}] => Stop Recording Record Message");
                IsRecoding = false;
                path = _voiceRecorder.StopRecord();
                _logger.Information($"[{nameof(RecordMessageService)}].[{nameof(SendAsync)}] => Finalizing Recording Message");
                byte[] record = await Task.Run(() => File.ReadAllBytes(path));

                if (record is null || record.Length == 0)
                    return false;

                var request = new RecordMessageRequest(
                    UlidGenerator.Generator(),
                    _session.User.ClientId,
                    _session.SelectedChatCard.ClientId,
                    record
                );

                return await SendAsync(request);
            }
            return false;
        }
    }
}
