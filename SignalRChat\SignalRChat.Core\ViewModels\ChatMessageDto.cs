﻿namespace SignalRChat.Core.ViewModels
{
    [JsonDerivedType(typeof(TextChatMessageDto), nameof(TextChatMessageDto.Message))]
    [JsonDerivedType(typeof(ImageChatMessageDto), nameof(ImageChatMessageDto.Image))]
    [JsonDerivedType(typeof(RecordChatMessageDto), nameof(RecordChatMessageDto.Record))]
    [JsonDerivedType(typeof(StreamChatMessageDto), nameof(StreamChatMessageDto.StreamId))]
    public class ChatMessageDto : ViewModelBase
    {
        [JsonConstructor]
        public ChatMessageDto(Ulid messageId, Ulid senderId, Ulid receiverId, DateTime dateTime, MessageType messageType)
        {
            MessageId = messageId;
            SenderId = senderId;
            ReceiverId = receiverId;
            DateTime = dateTime;
            MessageType = messageType;
        }

        public ChatMessageDto(ChatMessage chatMessage) : this(chatMessage.MessageId, chatMessage.SenderId, chatMessage.ReceiverId, chatMessage.DateTime, chatMessage.MessageType)
        {
        }

        public ChatMessageDto(IChatMessageRequest request, MessageType messageType) : this(request.MessageId, request.SenderId, request.ReceiverId, DateTime.Now, messageType)
        {
        }

        public ChatMessageDto(IChatMessageResponse response, MessageType messageType) : this(response.MessageId, response.SenderId, response.ReceiverId, DateTime.Now, messageType)
        {
        }

        [JsonInclude]
        public Ulid MessageId { get; private set; }
        [JsonInclude]
        public Ulid SenderId { get; private set; }
        [JsonInclude]
        public Ulid ReceiverId { get; private set; }
        [JsonInclude]
        public DateTime DateTime { get; private set; }
        [JsonInclude]
        public MessageType MessageType { get; private set; }

        public void SetDateTime(DateTime dateTime) => DateTime = dateTime;
    }

    public class TextChatMessageDto : ChatMessageDto
    {
        [JsonConstructor]
        public TextChatMessageDto(Ulid messageId, Ulid senderId, Ulid receiverId, string message) : base(messageId, senderId, receiverId, DateTime.Now, MessageType.Text)
        {
            Message = message;
        }

        public TextChatMessageDto(TextChatMessage textChatMessage) : base(textChatMessage)
        {
            Message = textChatMessage.Message;
        }

        public TextChatMessageDto(TextMessageRequest request) : base(request, MessageType.Text)
        {
            Message = request.Message;
        }

        public TextChatMessageDto(TextMessageResponse response) : base(response, MessageType.Text)
        {
            Message = response.Message;
        }

        [JsonInclude]
        public string Message { get; private set; }
    }

    public class ImageChatMessageDto : ChatMessageDto
    {
        [JsonConstructor]
        public ImageChatMessageDto(Ulid messageId, Ulid senderId, Ulid receiverId, byte[] image) : base(messageId, senderId, receiverId, DateTime.Now, MessageType.Image)
        {
            Image = image;
        }

        public ImageChatMessageDto(ImageChatMessage imageChatMessage) : base(imageChatMessage)
        {
            Image = imageChatMessage.Image;
        }

        public ImageChatMessageDto(ImageMessageRequest request) : base(request, MessageType.Image)
        {
            Image = request.Image;
        }

        public ImageChatMessageDto(ImageMessageResponse response) : base(response, MessageType.Image)
        {
            Image = response.Image;
        }

        [JsonInclude]
        public byte[] Image { get; private set; }
    }

    public class RecordChatMessageDto : ChatMessageDto
    {
        [JsonConstructor]
        public RecordChatMessageDto(Ulid messageId, Ulid senderId, Ulid receiverId, byte[] record) : base(messageId, senderId, receiverId, DateTime.Now, MessageType.Record)
        {
            Record = record;
        }

        public RecordChatMessageDto(RecordChatMessage recordChatMessage) : base(recordChatMessage)
        {
            Record = recordChatMessage.Record;
        }

        public RecordChatMessageDto(RecordMessageRequest request) : base(request, MessageType.Record)
        {
            Record = request.Record;
        }

        public RecordChatMessageDto(RecordMessageResponse response) : base(response, MessageType.Record)
        {
            Record = response.Record;
        }

        [JsonInclude]
        public byte[] Record { get; private set; }
    }

    public class StreamChatMessageDto : ChatMessageDto
    {
        [JsonConstructor]
        public StreamChatMessageDto(Ulid messageId, Ulid senderId, Ulid receiverId, Ulid streamId) : base(messageId, senderId, receiverId, DateTime.Now, MessageType.Stream)
        {
            StreamId = streamId;
        }

        public StreamChatMessageDto(StreamChatMessage streamChatMessage) : base(streamChatMessage)
        {
            StreamId = streamChatMessage.StreamId;
        }

        public StreamChatMessageDto(StreamMessageRequest request) : base(request, MessageType.Stream)
        {
            StreamId = request.StreamId;
        }

        public StreamChatMessageDto(StreamMessageResponse response) : base(response, MessageType.Stream)
        {
            StreamId = response.StreamId;
        }

        [JsonInclude]
        public Ulid StreamId { get; private set; }

        private byte[] _stream = null!;
        public byte[] Stream
        {
            get { return _stream; }
            set { _stream = value; OnPropertyChanged(); }
        }
    }
}
