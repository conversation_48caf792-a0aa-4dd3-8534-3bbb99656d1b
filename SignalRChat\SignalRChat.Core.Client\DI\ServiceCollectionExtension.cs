﻿namespace SignalRChat.Core.Client.DI
{
    public static class ServiceCollectionExtension
    {
        public static IServiceCollection AddSignalRChatClient(this IServiceCollection services)
            => services.AddSignalRChatClient<SignalRConnectionManager>(Logger.CreateSerilog());

        public static IServiceCollection AddSignalRChatClient<TSignalRConnectionManager>(this IServiceCollection services, ILogger logger)
            where TSignalRConnectionManager : class, ISignalRConnectionManager
        {
            services.AddHttpContextAccessor();

            services.AddSerilog(logger);

            services.AddSingleton<ISession, Session>();
            services.AddSingleton<ISignalRConnectionManager, TSignalRConnectionManager>();
            services.AddScoped<ILoginService, LoginService>();
            services.AddScoped<ILogoutService, LogoutService>();
            services.AddScoped<ICreateGroupService, CreateGroupService>();
            services.AddScoped<IJoinGroupService, JoinGroupService>();
            services.AddScoped<ILeaveGroupService, LeaveGroupService>();

            services.AddScoped<IReceiveChatMessageManager, ReceiveChatMessageManager>();
            services.AddScoped<ISendChatMessageManager, SendChatMessageManager>();
            services.AddScoped<IParticipantTypingService, ParticipantTypingService>();
            services.AddScoped<ITextMessageService, TextMessageService>();
            services.AddScoped<IImageMessageService, ImageMessageService>();
            services.AddScoped<IRecordMessageService, RecordMessageService>();
            services.AddScoped<IStreamMessageService, StreamMessageService>();
            services.AddScoped<IBuzzMessageService, BuzzMessageService>();

            return services;
        }
    }
}
