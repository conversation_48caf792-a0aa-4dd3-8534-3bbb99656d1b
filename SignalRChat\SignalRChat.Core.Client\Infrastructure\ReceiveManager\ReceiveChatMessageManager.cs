﻿namespace SignalRChat.Core.Client.Infrastructure.ReceiveManager
{
    public class ReceiveChatMessageManager(ISession session, ILogger logger) : IReceiveChatMessageManager
    {
        public async Task<bool> ReceiveAsync(IChatMessageResponse? response)
        {
            try
            {
                if (response == null)
                    return false;

                ChatCard? sender = session.GetChatCard(response.SenderId);
                if (sender != null)
                {
                    logger.Information($"[{nameof(RecordMessageService)}].[{nameof(ReceiveAsync)}] => Start Receiving Message From [{response.SenderId}]");
                    bool notifyReciver = true;
                    switch (response)
                    {
                        case TextMessageResponse textMessageResponse:
                            var textChatMessageDto = new TextChatMessageDto(textMessageResponse);
                            sender.ChatMessages.Add(textChatMessageDto);
                            break;
                        case ImageMessageResponse imageMessageResponse:
                            var imageChatMessageDto = new ImageChatMessageDto(imageMessageResponse);
                            sender.ChatMessages.Add(imageChatMessageDto);
                            break;
                        case RecordMessageResponse recordMessageResponse:
                            var recordChatMessageDto = new RecordChatMessageDto(recordMessageResponse);
                            sender.ChatMessages.Add(recordChatMessageDto);
                            break;
                        case StreamMessageResponse streamMessageResponse:
                            var streamChatMessageDto = new StreamChatMessageDto(streamMessageResponse)
                            {
                                Stream = streamMessageResponse.Stream
                            };
                            var message = sender.ChatMessages.OfType<StreamChatMessageDto>().SingleOrDefault(x => x.SenderId == response.SenderId && x.StreamId == streamChatMessageDto.StreamId);
                            if (message != null)
                                message.Stream = streamChatMessageDto.Stream;
                            else
                                sender.ChatMessages.Add(streamChatMessageDto);
                            break;
                        case BuzzMessageResponse buzzMessageResponse:
                            notifyReciver = false;
                            break;
                        case ParticipantTypingResponse participantTypingResponse:
                            if (!sender.IsTyping)
                            {
                                sender.IsTyping = true;
                                await Task.Delay(1500);
                                sender.IsTyping = false;
                            }
                            notifyReciver = false;
                            break;
                        default:
                            logger.Warning($"[{nameof(ReceiveChatMessageManager)}].[{nameof(ReceiveAsync)}] => Unknown message type");
                            return false;
                    }
                    if (!(session.SelectedChatCard != null && sender.ClientId.Equals(session.SelectedChatCard.ClientId)) && notifyReciver)
                        sender.HasSentNewMessage = true;
                    logger.Information($"[{nameof(RecordMessageService)}].[{nameof(ReceiveAsync)}] => End Receiving Message From [{response.SenderId}]");
                }
                return true;
            }
            catch (Exception ex)
            {
                logger.Error(ex, $"[{nameof(ReceiveChatMessageManager)}].[{nameof(ReceiveAsync)}] => Failed Receiving Message");
                return false;
            }
        }
    }
}
