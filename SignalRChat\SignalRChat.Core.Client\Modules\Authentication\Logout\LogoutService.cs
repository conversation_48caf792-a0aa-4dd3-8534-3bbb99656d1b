﻿namespace SignalRChat.Core.Client.Modules.Authentication.Logout
{
    public delegate void LogoutRequestedEventHandler(LogoutRequest? request);
    public delegate void LogoutReceivedEventHandler(LogoutResponse? response);

    public interface ILogoutService : IBaseModuleService<LogoutRequest, LogoutResponse>
    {
        event LogoutReceivedEventHandler OnNewLogout;
        Task<bool> SendAsync();
    }

    public class LogoutService : ILogoutService
    {
        private readonly ISignalRConnectionManager _signalRConnectionManager;
        private readonly ISession _session;
        private readonly ILogger _logger;

        public event LogoutReceivedEventHandler OnNewLogout;

        public LogoutService(ISignalRConnectionManager signalRConnectionManager, ISession session, ILogger logger)
        {
            _signalRConnectionManager = signalRConnectionManager;
            _session = session;
            _logger = logger;

            _signalRConnectionManager.OnLogoutReceived += Receive;
        }

        public async Task<bool> SendAsync()
        {
            var request = new LogoutRequest(_session.User.ClientId);
            if (CanSend(request))
                return await SendAsync(request);
            return false;
        }

        public bool CanSend(LogoutRequest? request)
        {
            return
                request != null
                && request.Id.IsValid()
                && _signalRConnectionManager.IsConnected;
        }

        public async Task<bool> SendAsync(LogoutRequest request)
        {
            try
            {
                _logger.Information($"[{nameof(LogoutService)}].[{nameof(Logout)}] => Start Logging Out");
                await _signalRConnectionManager.LogoutAsync(request);
                _logger.Information($"[{nameof(LogoutService)}].[{nameof(Logout)}] => End Logging Out");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"[{nameof(LogoutService)}].[{nameof(SendAsync)}] => Failed Logging Out");
                return false;
            }
        }

        public void Receive(LogoutResponse? response)
        {
            if (response is null)
                return;

            OnNewLogout?.Invoke(response);
        }
    }
}
