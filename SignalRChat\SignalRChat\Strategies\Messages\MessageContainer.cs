﻿namespace SignalRChat.Strategies.Messages
{
    public class MessageContainer : TableLayoutPanel, IMessageContainer, IDisposable
    {
        public MessageContainer()
        {
            InitializeLayout();
        }

        private void InitializeLayout()
        {
            this.SafelyInvokeAction(() =>
            {
                Dock = DockStyle.Fill;
                ColumnCount = 2;
                RowCount = 2;
                ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
                ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 60F));
                RowStyles.Add(new RowStyle(SizeType.AutoSize));
                RowStyles.Add(new RowStyle(SizeType.Absolute, 5F));
            });
        }

        public void Add(object obj, int column, int row)
        {
            if (obj is Control control)
            {
                this.SafelyInvokeAction(() =>
                {
                    Controls.Add(control, column, row);
                    SetColumnSpan(control, 2);
                });
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Dispose managed resources
                foreach (Control control in Controls)
                {
                    control.Dispose();
                }
            }

            base.Dispose(disposing);
        }
    }
}
