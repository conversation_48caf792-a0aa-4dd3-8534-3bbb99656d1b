namespace SignalRChat.Core.Modules.ChatMessage.StreamMessage
{
    public record StreamMessageRequest(Ulid MessageId, Ulid SenderId, Ulid ReceiverId, Ulid StreamId) : BaseChatMessageRequest(MessageId, SenderId, ReceiverId)
    {
        [JsonInclude]
        public byte[] Stream { get; set; }

        public override bool IsValid()
        {
            return
                base.IsValid()
                && StreamId.IsValid()
                && Stream != null
                && Stream.Length > 0;
        }
    }

    public record StreamMessageResponse(Ulid MessageId, Ulid SenderId, Ulid ReceiverId, Ulid StreamId, byte[] Stream) : BaseChatMessageResponse(MessageId, SenderId, ReceiverId, false);
}
