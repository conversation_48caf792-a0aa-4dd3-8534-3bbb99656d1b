﻿namespace SignalRChat.Core.Client.Modules.ChatMessage.BuzzMessage
{
    public delegate void BuzzMessageRequestedEventHandler(BuzzMessageRequest? request);
    public delegate void BuzzMessageReceivedEventHandler(BuzzMessageResponse? response);

    public interface IBuzzMessageService : IBaseModuleService<BuzzMessageRequest, BuzzMessageResponse>
    {
        event BuzzMessageReceivedEventHandler OnBuzzMessage;
        Task<bool> SendAsync(Ulid recipientId);
    }

    public class BuzzMessageService : BaseChatMessage, IBuzzMessageService
    {
        public event BuzzMessageReceivedEventHandler OnBuzzMessage;

        public BuzzMessageService(
            ISignalRConnectionManager signalRConnectionManager,
            ISendChatMessageManager sendChatMessageManager,
            IReceiveChatMessageManager receiveChatMessageManager,
            ISession session,
            ILogger logger) : base(signalRConnectionManager, sendChatMessageManager, receiveChatMessageManager, session, logger)
        {
            _signalRConnectionManager.OnBuzzMessageReceived += Receive;
        }

        public async Task<bool> SendAsync(Ulid recipientId)
        {
            var request = new BuzzMessageRequest(
                UlidGenerator.Generator(),
                _session.User.ClientId,
                recipientId);

            if (CanSend(request))
                return await SendAsync(request);
            return false;
        }

        public bool CanSend(BuzzMessageRequest? request)
        {
            return base.CanSend(request);
        }

        public async Task<bool> SendAsync(BuzzMessageRequest request)
        {
            return await _sendChatMessageManager.SendAsync(request);
        }

        public async void Receive(BuzzMessageResponse? response)
        {
            if (response is null)
                return;

            await _receiveChatMessageManager.ReceiveAsync(response);
            OnBuzzMessage?.Invoke(response);
        }
    }
}
