﻿namespace SignalRChat.Core.Client.Strategies.Messages.ImageMessage
{
    public class ImageMessageDisplayStrategy : IMessageDisplayStrategy
    {
        private IImageMessageView _view;

        public ImageMessageDisplayStrategy(IImageMessageView view)
        {
            _view = view;
        }

        public void DisplayMessage(IMessageContainer tlpMain, ChatMessageDto chatMessageDto)
        {
            if (chatMessageDto != null && chatMessageDto is ImageChatMessageDto imageChatMessageDto)
            {
                _view.Size = new Size(150, 150);
                _view.Load(Converters.SaveImageAsTempFile(imageChatMessageDto.Image));
            }

            tlpMain.Add(_view, 0, 0);
        }

        public int CalculateHeight()
        {
            if (_view is null)
                return default;

            int imageMessageHeight = _view.Height + 10;
            return imageMessageHeight;
        }

        public void SetBackColor(Color color) => _view.BackColor = color;
    }
}
