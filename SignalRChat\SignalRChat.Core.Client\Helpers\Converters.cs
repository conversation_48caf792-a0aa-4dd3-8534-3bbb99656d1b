﻿namespace SignalRChat.Core.Client.Helpers
{
    public static class Converters
    {
        public static byte[] ImageToByteArray(Image image)
        {
            if (image != null)
            {
                using (MemoryStream memoryStream = new MemoryStream())
                {
                    ImageFormat format = image.RawFormat.Equals(ImageFormat.Jpeg) ? ImageFormat.Jpeg : ImageFormat.Png;
                    image.Save(memoryStream, format);
                    return memoryStream.ToArray();
                }
            }
            return Array.Empty<byte>();
        }

        public static Image? ByteArrayToImage(byte[] byteArray)
        {
            if (byteArray?.Length > 0)
            {
                using (MemoryStream memoryStream = new MemoryStream(byteArray))
                {
                    return Image.FromStream(memoryStream, true);
                }
            }
            return null;
        }

        public static string SaveImageAsTempFile(byte[] imageBytes)
        {
            // Generate a unique file name with a .jpg extension in the temporary directory
            string tempFilePath = Path.Combine(Path.GetTempPath(), UlidGenerator.Generator().ToString() + ".jpg");

            // Write the byte array to the file
            File.WriteAllBytes(tempFilePath, imageBytes);

            // Return the path to the saved file
            return tempFilePath;
        }
    }
}
