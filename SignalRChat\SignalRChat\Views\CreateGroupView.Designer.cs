﻿namespace SignalRChat.Views
{
    partial class CreateGroupView
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            tableLayoutPanel1 = new TableLayoutPanel();
            btnCreate = new Button();
            pbxProfilePicture = new PictureBox();
            txtGroupName = new TextBox();
            tableLayoutPanel1.SuspendLayout();
            ((ISupportInitialize)pbxProfilePicture).BeginInit();
            SuspendLayout();
            // 
            // tableLayoutPanel1
            // 
            tableLayoutPanel1.ColumnCount = 3;
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 35F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 35F));
            tableLayoutPanel1.Controls.Add(btnCreate, 1, 3);
            tableLayoutPanel1.Controls.Add(pbxProfilePicture, 1, 1);
            tableLayoutPanel1.Controls.Add(txtGroupName, 1, 2);
            tableLayoutPanel1.Dock = DockStyle.Fill;
            tableLayoutPanel1.Location = new Point(0, 0);
            tableLayoutPanel1.Name = "tableLayoutPanel1";
            tableLayoutPanel1.RowCount = 5;
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 50F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 50F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel1.Size = new Size(362, 407);
            tableLayoutPanel1.TabIndex = 1;
            // 
            // btnCreate
            // 
            btnCreate.Dock = DockStyle.Fill;
            btnCreate.Location = new Point(38, 325);
            btnCreate.Name = "btnCreate";
            btnCreate.Size = new Size(286, 44);
            btnCreate.TabIndex = 0;
            btnCreate.Text = "Create";
            btnCreate.UseVisualStyleBackColor = true;
            // 
            // pbxProfilePicture
            // 
            pbxProfilePicture.Dock = DockStyle.Fill;
            pbxProfilePicture.Image = Properties.Resources.blank_user_icon;
            pbxProfilePicture.Location = new Point(38, 38);
            pbxProfilePicture.Name = "pbxProfilePicture";
            pbxProfilePicture.Size = new Size(286, 231);
            pbxProfilePicture.SizeMode = PictureBoxSizeMode.CenterImage;
            pbxProfilePicture.TabIndex = 1;
            pbxProfilePicture.TabStop = false;
            // 
            // txtGroupName
            // 
            txtGroupName.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            txtGroupName.Location = new Point(38, 285);
            txtGroupName.Name = "txtGroupName";
            txtGroupName.Size = new Size(286, 23);
            txtGroupName.TabIndex = 2;
            // 
            // CreateGroupView
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(362, 407);
            Controls.Add(tableLayoutPanel1);
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "CreateGroupView";
            Text = "GroupView";
            tableLayoutPanel1.ResumeLayout(false);
            tableLayoutPanel1.PerformLayout();
            ((ISupportInitialize)pbxProfilePicture).EndInit();
            ResumeLayout(false);
        }

        #endregion

        private TableLayoutPanel tableLayoutPanel1;
        private Button btnCreate;
        private PictureBox pbxProfilePicture;
        private TextBox txtGroupName;
    }
}