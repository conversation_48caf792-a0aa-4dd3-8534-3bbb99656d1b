﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>1701;1702;CA1416;CS8618</NoWarn>
    <WarningsAsErrors>$(WarningsAsErrors);NU1605;CS8600;CS8601;CS8602;CS8603;CS8604;CS8605;CS8613;CS8622;CS8625;CS8765;CS8767;SYSLIB0006;SYSLIB0014;</WarningsAsErrors>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <NoWarn>1701;1702;CA1416;CS8618</NoWarn>
    <WarningsAsErrors>$(WarningsAsErrors);NU1605;CS8600;CS8601;CS8602;CS8603;CS8604;CS8605;CS8613;CS8622;CS8625;CS8765;CS8767;SYSLIB0006;SYSLIB0014;</WarningsAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\SignalRChat.Core.Client\SignalRChat.Core.Client.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="CustomControls\ContactCard.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="CustomControls\Message.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="CustomControls\MessageList.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Update="Strategies\Messages\StreamMessage.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="Views\BaseView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Views\ChatView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Views\JoinGroupView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Views\LoginView.cs">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>