﻿namespace SignalRChat.Core.Helpers
{
    public static class BindingListExtensions
    {
        public static T TryAdd<T>(this BindingList<T> values, T value) where T : IComparer<T>
        {
            IComparer<T> comparer = value;

            if (values == null)
                throw new ArgumentNullException(nameof(values));

            if (comparer == null)
                throw new ArgumentNullException(nameof(value));

            // Check if the item already exists in the list
            foreach (var existingValue in values)
            {
                if (comparer.Compare(existingValue, value) == 0)
                    continue; // Item already exists, so don't add
            }

            values.Add(value);
            return value; // Return the added item
        }

        public static T? TryUpdate<T>(this BindingList<T> values, T value) where T : IComparer<T>
        {
            IComparer<T> comparer = value;

            if (values == null)
                throw new ArgumentNullException(nameof(values));

            if (comparer == null)
                throw new ArgumentNullException(nameof(value));

            for (int i = 0; i < values.Count; i++)
            {
                if (comparer.Compare(values[i], value) == 0)
                {
                    var oldValue = values[i];
                    values[i] = value; // Update the item
                    return oldValue; // Return the old item
                }
            }

            return default; // Item not found, no update performed
        }

        public static T? TryRemove<T>(this BindingList<T> values, T value) where T : IComparer<T>
        {
            IComparer<T> comparer = value;

            if (values == null)
                throw new ArgumentNullException(nameof(values));

            if (comparer == null)
                throw new ArgumentNullException(nameof(value));

            for (int i = 0; i < values.Count; i++)
            {
                if (comparer.Compare(values[i], value) == 0)
                {
                    var removedValue = values[i];
                    values.RemoveAt(i); // Remove the item
                    return removedValue; // Return the removed item
                }
            }

            return default; // Item not found, no removal performed
        }
    }
}
