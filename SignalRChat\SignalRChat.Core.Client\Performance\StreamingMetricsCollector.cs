using SignalRChat.Core.Client.Configuration;
using SignalRChat.Core.Client.Network;

namespace SignalRChat.Core.Client.Performance
{
    /// <summary>
    /// Comprehensive streaming metrics
    /// </summary>
    public class StreamingMetrics
    {
        public DateTime Timestamp { get; set; } = DateTime.Now;
        
        // Performance metrics
        public PerformanceMetrics Performance { get; set; } = new();
        
        // Network metrics
        public BandwidthStatistics Bandwidth { get; set; } = new();
        
        // Streaming-specific metrics
        public int CurrentFrameRate { get; set; }
        public int TargetFrameRate { get; set; }
        public (int Width, int Height) CurrentResolution { get; set; }
        public (int Width, int Height) TargetResolution { get; set; }
        public double ScaleFactor { get; set; }
        public VideoQuality CurrentQuality { get; set; }
        public int JpegQuality { get; set; }
        public CompressionType CompressionType { get; set; }
        
        // Quality indicators
        public double QualityScore { get; set; }
        public double PerformanceScore { get; set; }
        public double NetworkScore { get; set; }
        public double OverallScore { get; set; }
        
        // Efficiency metrics
        public double CompressionEfficiency { get; set; }
        public double BandwidthUtilization { get; set; }
        public double CpuEfficiency { get; set; }
        public double MemoryEfficiency { get; set; }
        
        // Adaptation metrics
        public int FrameRateAdaptations { get; set; }
        public int ResolutionAdaptations { get; set; }
        public int QualityAdaptations { get; set; }
        public TimeSpan TotalStreamingTime { get; set; }
    }

    /// <summary>
    /// Collects and analyzes comprehensive streaming metrics
    /// </summary>
    public class StreamingMetricsCollector : IDisposable
    {
        private readonly StreamingConfiguration _config;
        private readonly PerformanceMonitor _performanceMonitor;
        private readonly BandwidthMonitor _bandwidthMonitor;
        private readonly Timer _metricsTimer;
        private readonly Queue<StreamingMetrics> _metricsHistory;
        private readonly object _lockObject = new();

        private DateTime _streamingStartTime;
        private StreamingMetrics _currentMetrics;
        private int _frameRateAdaptations;
        private int _resolutionAdaptations;
        private int _qualityAdaptations;
        private (int Width, int Height) _lastResolution;
        private int _lastFrameRate;
        private VideoQuality _lastQuality;
        private bool _disposed;

        public event Action<StreamingMetrics>? MetricsUpdated;

        public StreamingMetrics CurrentMetrics => _currentMetrics;
        public bool IsCollecting { get; private set; }

        public StreamingMetricsCollector(
            StreamingConfiguration config,
            PerformanceMonitor performanceMonitor,
            BandwidthMonitor bandwidthMonitor)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _performanceMonitor = performanceMonitor ?? throw new ArgumentNullException(nameof(performanceMonitor));
            _bandwidthMonitor = bandwidthMonitor ?? throw new ArgumentNullException(nameof(bandwidthMonitor));

            _metricsHistory = new Queue<StreamingMetrics>();
            _currentMetrics = new StreamingMetrics();
            _metricsTimer = new Timer(CollectMetrics, null, Timeout.Infinite, Timeout.Infinite);

            _lastResolution = (config.Width, config.Height);
            _lastFrameRate = config.TargetFrameRate;
            _lastQuality = config.Quality;
        }

        /// <summary>
        /// Starts metrics collection
        /// </summary>
        public void StartCollection(TimeSpan interval)
        {
            if (_disposed) return;

            IsCollecting = true;
            _streamingStartTime = DateTime.Now;
            _frameRateAdaptations = 0;
            _resolutionAdaptations = 0;
            _qualityAdaptations = 0;

            _metricsTimer.Change(TimeSpan.Zero, interval);
        }

        /// <summary>
        /// Stops metrics collection
        /// </summary>
        public void StopCollection()
        {
            IsCollecting = false;
            _metricsTimer.Change(Timeout.Infinite, Timeout.Infinite);
        }

        /// <summary>
        /// Updates streaming parameters for tracking adaptations
        /// </summary>
        public void UpdateStreamingParameters(int frameRate, (int Width, int Height) resolution, VideoQuality quality)
        {
            lock (_lockObject)
            {
                if (frameRate != _lastFrameRate)
                {
                    _frameRateAdaptations++;
                    _lastFrameRate = frameRate;
                }

                if (resolution != _lastResolution)
                {
                    _resolutionAdaptations++;
                    _lastResolution = resolution;
                }

                if (quality != _lastQuality)
                {
                    _qualityAdaptations++;
                    _lastQuality = quality;
                }
            }
        }

        /// <summary>
        /// Collects comprehensive metrics
        /// </summary>
        private void CollectMetrics(object? state)
        {
            if (_disposed || !IsCollecting) return;

            try
            {
                var metrics = new StreamingMetrics
                {
                    Timestamp = DateTime.Now,
                    Performance = _performanceMonitor.CurrentMetrics,
                    Bandwidth = _bandwidthMonitor.CurrentStatistics,
                    CurrentFrameRate = _lastFrameRate,
                    TargetFrameRate = _config.TargetFrameRate,
                    CurrentResolution = _lastResolution,
                    TargetResolution = (_config.Width, _config.Height),
                    ScaleFactor = _config.ScaleFactor,
                    CurrentQuality = _lastQuality,
                    JpegQuality = _config.JpegQuality,
                    CompressionType = _config.CompressionType,
                    FrameRateAdaptations = _frameRateAdaptations,
                    ResolutionAdaptations = _resolutionAdaptations,
                    QualityAdaptations = _qualityAdaptations,
                    TotalStreamingTime = DateTime.Now - _streamingStartTime
                };

                // Calculate quality scores
                CalculateQualityScores(metrics);
                
                // Calculate efficiency metrics
                CalculateEfficiencyMetrics(metrics);

                lock (_lockObject)
                {
                    _currentMetrics = metrics;
                    
                    // Keep history of last 100 metrics
                    _metricsHistory.Enqueue(metrics);
                    while (_metricsHistory.Count > 100)
                        _metricsHistory.Dequeue();
                }

                MetricsUpdated?.Invoke(metrics);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error collecting streaming metrics: {ex.Message}");
            }
        }

        /// <summary>
        /// Calculates quality scores (0-100)
        /// </summary>
        private void CalculateQualityScores(StreamingMetrics metrics)
        {
            // Performance score based on CPU, memory, and frame processing
            var cpuScore = Math.Max(0, 100 - metrics.Performance.CpuUsagePercent);
            var memoryScore = Math.Max(0, 100 - (metrics.Performance.MemoryUsageMB * 100.0 / _config.MaxMemoryUsageMB));
            var frameScore = metrics.Performance.DroppedFrames == 0 ? 100 : Math.Max(0, 100 - metrics.Performance.DroppedFrames * 10);
            metrics.PerformanceScore = (cpuScore + memoryScore + frameScore) / 3;

            // Network score based on bandwidth utilization and packet loss
            var bandwidthScore = Math.Max(0, 100 - metrics.BandwidthUtilization);
            var latencyScore = metrics.Bandwidth.AverageLatency.TotalMilliseconds < 100 ? 100 : 
                              Math.Max(0, 100 - metrics.Bandwidth.AverageLatency.TotalMilliseconds / 10);
            var lossScore = Math.Max(0, 100 - metrics.Bandwidth.PacketLossPercent * 10);
            metrics.NetworkScore = (bandwidthScore + latencyScore + lossScore) / 3;

            // Quality score based on resolution and frame rate vs targets
            var resolutionRatio = (double)(metrics.CurrentResolution.Width * metrics.CurrentResolution.Height) /
                                 (metrics.TargetResolution.Width * metrics.TargetResolution.Height);
            var resolutionScore = Math.Min(100, resolutionRatio * 100);
            
            var frameRateRatio = (double)metrics.CurrentFrameRate / metrics.TargetFrameRate;
            var frameRateScore = Math.Min(100, frameRateRatio * 100);
            
            var qualityScore = GetQualityScore(metrics.CurrentQuality);
            metrics.QualityScore = (resolutionScore + frameRateScore + qualityScore) / 3;

            // Overall score
            metrics.OverallScore = (metrics.PerformanceScore + metrics.NetworkScore + metrics.QualityScore) / 3;
        }

        /// <summary>
        /// Gets quality score for video quality setting
        /// </summary>
        private double GetQualityScore(VideoQuality quality)
        {
            return quality switch
            {
                VideoQuality.Low => 25,
                VideoQuality.Medium => 50,
                VideoQuality.High => 75,
                VideoQuality.Ultra => 100,
                _ => 50
            };
        }

        /// <summary>
        /// Calculates efficiency metrics
        /// </summary>
        private void CalculateEfficiencyMetrics(StreamingMetrics metrics)
        {
            // Compression efficiency (higher is better)
            if (metrics.Performance.AverageFrameSize > 0)
            {
                var uncompressedSize = metrics.CurrentResolution.Width * metrics.CurrentResolution.Height * 3; // 24bpp
                metrics.CompressionEfficiency = Math.Min(100, (1.0 - (double)metrics.Performance.AverageFrameSize / uncompressedSize) * 100);
            }

            // Bandwidth utilization
            metrics.BandwidthUtilization = Math.Min(100, (metrics.Bandwidth.CurrentBandwidthKbps * 100.0) / _config.MaxBandwidthKbps);

            // CPU efficiency (performance per CPU usage)
            metrics.CpuEfficiency = metrics.Performance.CpuUsagePercent > 0 ? 
                metrics.Performance.CurrentFrameRate / metrics.Performance.CpuUsagePercent * 100 : 0;

            // Memory efficiency (performance per memory usage)
            metrics.MemoryEfficiency = metrics.Performance.MemoryUsageMB > 0 ? 
                metrics.Performance.CurrentFrameRate / metrics.Performance.MemoryUsageMB * 100 : 0;
        }

        /// <summary>
        /// Gets metrics history
        /// </summary>
        public IEnumerable<StreamingMetrics> GetMetricsHistory()
        {
            lock (_lockObject)
            {
                return _metricsHistory.ToArray();
            }
        }

        /// <summary>
        /// Gets average metrics over a time period
        /// </summary>
        public StreamingMetrics GetAverageMetrics(TimeSpan period)
        {
            lock (_lockObject)
            {
                var cutoff = DateTime.Now - period;
                var relevantMetrics = _metricsHistory.Where(m => m.Timestamp >= cutoff).ToList();

                if (!relevantMetrics.Any())
                    return new StreamingMetrics();

                return new StreamingMetrics
                {
                    Timestamp = DateTime.Now,
                    CurrentFrameRate = (int)relevantMetrics.Average(m => m.CurrentFrameRate),
                    QualityScore = relevantMetrics.Average(m => m.QualityScore),
                    PerformanceScore = relevantMetrics.Average(m => m.PerformanceScore),
                    NetworkScore = relevantMetrics.Average(m => m.NetworkScore),
                    OverallScore = relevantMetrics.Average(m => m.OverallScore),
                    CompressionEfficiency = relevantMetrics.Average(m => m.CompressionEfficiency),
                    BandwidthUtilization = relevantMetrics.Average(m => m.BandwidthUtilization),
                    CpuEfficiency = relevantMetrics.Average(m => m.CpuEfficiency),
                    MemoryEfficiency = relevantMetrics.Average(m => m.MemoryEfficiency),
                    FrameRateAdaptations = relevantMetrics.Max(m => m.FrameRateAdaptations),
                    ResolutionAdaptations = relevantMetrics.Max(m => m.ResolutionAdaptations),
                    QualityAdaptations = relevantMetrics.Max(m => m.QualityAdaptations)
                };
            }
        }

        /// <summary>
        /// Generates a performance report
        /// </summary>
        public string GeneratePerformanceReport()
        {
            var metrics = _currentMetrics;
            var avgMetrics = GetAverageMetrics(TimeSpan.FromMinutes(5));

            return $@"
=== Streaming Performance Report ===
Timestamp: {metrics.Timestamp:yyyy-MM-dd HH:mm:ss}
Streaming Duration: {metrics.TotalStreamingTime:hh\:mm\:ss}

=== Current Performance ===
Overall Score: {metrics.OverallScore:F1}/100
Performance Score: {metrics.PerformanceScore:F1}/100
Network Score: {metrics.NetworkScore:F1}/100
Quality Score: {metrics.QualityScore:F1}/100

=== System Resources ===
CPU Usage: {metrics.Performance.CpuUsagePercent:F1}%
Memory Usage: {metrics.Performance.MemoryUsageMB} MB
Frame Rate: {metrics.CurrentFrameRate}/{metrics.TargetFrameRate} FPS
Dropped Frames: {metrics.Performance.DroppedFrames}

=== Network ===
Bandwidth: {metrics.Bandwidth.CurrentBandwidthKbps} Kbps
Utilization: {metrics.BandwidthUtilization:F1}%
Packet Loss: {metrics.Bandwidth.PacketLossPercent:F1}%
Latency: {metrics.Bandwidth.AverageLatency.TotalMilliseconds:F0} ms

=== Quality ===
Resolution: {metrics.CurrentResolution.Width}x{metrics.CurrentResolution.Height} (Target: {metrics.TargetResolution.Width}x{metrics.TargetResolution.Height})
Quality: {metrics.CurrentQuality}
JPEG Quality: {metrics.JpegQuality}%
Compression: {metrics.CompressionType}

=== Adaptations ===
Frame Rate Adaptations: {metrics.FrameRateAdaptations}
Resolution Adaptations: {metrics.ResolutionAdaptations}
Quality Adaptations: {metrics.QualityAdaptations}

=== Efficiency (5-min average) ===
Compression Efficiency: {avgMetrics.CompressionEfficiency:F1}%
CPU Efficiency: {avgMetrics.CpuEfficiency:F1}
Memory Efficiency: {avgMetrics.MemoryEfficiency:F1}
";
        }

        public void Dispose()
        {
            if (_disposed) return;

            _disposed = true;
            StopCollection();
            _metricsTimer?.Dispose();
        }
    }
}
