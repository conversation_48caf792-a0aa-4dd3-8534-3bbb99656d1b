﻿global using Microsoft.AspNetCore.SignalR.Client;
global using Microsoft.Extensions.DependencyInjection;
global using NAudio;
global using NAudio.Wave;
global using Serilog;
global using SignalRChat.Core.Client.Helpers;
global using SignalRChat.Core.Client.Infrastructure;
global using SignalRChat.Core.Client.Infrastructure.ConnectionManager;
global using SignalRChat.Core.Client.Infrastructure.ReceiveManager;
global using SignalRChat.Core.Client.Infrastructure.SendManager;
global using SignalRChat.Core.Client.Modules.Authentication.Login;
global using SignalRChat.Core.Client.Modules.Authentication.Logout;
global using SignalRChat.Core.Client.Modules.ChatMessage.BuzzMessage;
global using SignalRChat.Core.Client.Modules.ChatMessage.ImageMessage;
global using SignalRChat.Core.Client.Modules.ChatMessage.ParticipantTyping;
global using SignalRChat.Core.Client.Modules.ChatMessage.RecordMessage;
global using SignalRChat.Core.Client.Modules.ChatMessage.StreamMessage;
global using SignalRChat.Core.Client.Modules.ChatMessage.TextMessage;
global using SignalRChat.Core.Client.Modules.Group.CreateGroup;
global using SignalRChat.Core.Client.Modules.Group.JoinGroup;
global using SignalRChat.Core.Client.Modules.Group.LeaveGroup;
global using SignalRChat.Core.Helpers;
global using SignalRChat.Core.Models;
global using SignalRChat.Core.Modules.Authentication.Login;
global using SignalRChat.Core.Modules.Authentication.Logout;
global using SignalRChat.Core.Modules.ChatMessage;
global using SignalRChat.Core.Modules.ChatMessage.BuzzMessage;
global using SignalRChat.Core.Modules.ChatMessage.ImageMessage;
global using SignalRChat.Core.Modules.ChatMessage.ParticipantTyping;
global using SignalRChat.Core.Modules.ChatMessage.RecordMessage;
global using SignalRChat.Core.Modules.ChatMessage.StreamMessage;
global using SignalRChat.Core.Modules.ChatMessage.TextMessage;
global using SignalRChat.Core.ViewModels;
global using System.ComponentModel;
global using System.Diagnostics;
global using System.Drawing;
global using System.Drawing.Drawing2D;
global using System.Drawing.Imaging;
global using System.Globalization;
global using System.Net;
global using System.Runtime.InteropServices;
global using System.Text.Json.Serialization;
