﻿namespace SignalRChat.Views
{
    partial class JoinGroupView
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            tableLayoutPanel1 = new TableLayoutPanel();
            btnJoin = new Button();
            txtGroupName = new TextBox();
            tableLayoutPanel1.SuspendLayout();
            SuspendLayout();
            // 
            // tableLayoutPanel1
            // 
            tableLayoutPanel1.ColumnCount = 3;
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 35F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 35F));
            tableLayoutPanel1.Controls.Add(btnJoin, 1, 2);
            tableLayoutPanel1.Controls.Add(txtGroupName, 1, 1);
            tableLayoutPanel1.Dock = DockStyle.Fill;
            tableLayoutPanel1.Location = new Point(0, 0);
            tableLayoutPanel1.Name = "tableLayoutPanel1";
            tableLayoutPanel1.RowCount = 5;
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 50F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 50F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel1.Size = new Size(362, 172);
            tableLayoutPanel1.TabIndex = 1;
            // 
            // btnJoin
            // 
            btnJoin.Dock = DockStyle.Fill;
            btnJoin.Location = new Point(38, 88);
            btnJoin.Name = "btnJoin";
            btnJoin.Size = new Size(286, 44);
            btnJoin.TabIndex = 0;
            btnJoin.Text = "Join";
            btnJoin.UseVisualStyleBackColor = true;
            // 
            // txtGroupName
            // 
            txtGroupName.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            txtGroupName.Location = new Point(38, 48);
            txtGroupName.Name = "txtGroupName";
            txtGroupName.Size = new Size(286, 23);
            txtGroupName.TabIndex = 2;
            // 
            // JoinGroupView
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(362, 172);
            Controls.Add(tableLayoutPanel1);
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "JoinGroupView";
            Text = "GroupView";
            tableLayoutPanel1.ResumeLayout(false);
            tableLayoutPanel1.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private TableLayoutPanel tableLayoutPanel1;
        private Button btnJoin;
        private TextBox txtGroupName;
    }
}